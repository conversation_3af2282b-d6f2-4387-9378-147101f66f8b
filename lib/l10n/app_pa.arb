{"@@locale": "pa", "appTitle": "ਰੇਲਓਪਸ", "@appTitle": {"description": "The title of the application"}, "cancel": "", "confirm": "", "delete": "", "edit": "", "error": "", "home": "", "loading": "", "login": "ਲਾਗਇਨ", "@login": {"description": "Login button text"}, "logout": "", "next": "", "no": "", "ok": "", "password": "", "previous": "", "refresh": "", "retry": "", "save": "", "search": "", "settings": "", "submit": "", "username": "", "welcome": "ਰੇਲਓਪਸ ਵਿੱਚ ਤੁਹਾਡਾ ਸਵਾਗਤ ਹੈ", "@welcome": {"description": "Welcome message displayed on home screen"}, "yes": "", "test_welcome": "ਸਵਾਗਤ", "@test_welcome": {"description": "Welcome message for testing", "context": "test_context"}, "test_login": "ਲਾਗਇਨ", "@test_login": {"description": "Login button text", "context": "button_labels"}, "text_menu": "ਮੇਨੂ", "@text_menu": {"description": "Text for drawer menu header", "context": "custom_drawer"}, "text_train_tracker": "ਟ੍ਰੇਨ ਟ੍ਰੈਕਰ", "@text_train_tracker": {"description": "Text from text_widgets: Train Tracker", "context": "text_widgets"}, "text_assign_ca": "CA ਨਿਯੁਕਤ ਕਰੋ", "@text_assign_ca": {"description": "Text from text_widgets: Assign CA", "context": "text_widgets"}, "text_assign_cs": "CS ਨਿਯੁਕਤ ਕਰੋ", "@text_assign_cs": {"description": "Text from text_widgets: Assign CS", "context": "text_widgets"}, "text_pnr_details": "PNR ਵੇਰਵੇ", "@text_pnr_details": {"description": "Text from text_widgets: PNR Details", "context": "text_widgets"}, "text_passenger_chart": "ਯਾਤਰੀ ਚਾਰਟ", "@text_passenger_chart": {"description": "Text from text_widgets: Passenger Chart", "context": "text_widgets"}, "text_map_screen": "ਨਕਸ਼ਾ ਸਕ੍ਰੀਨ", "@text_map_screen": {"description": "Text from text_widgets: Map Screen", "context": "text_widgets"}, "text_configuration": "ਸੰਰਚਨਾ", "@text_configuration": {"description": "Text from text_widgets: Configuration", "context": "text_widgets"}, "text_reports": "ਰਿਪੋਰਟਾਂ", "@text_reports": {"description": "Text from text_widgets: Reports", "context": "text_widgets"}, "text_passenger_feedback": "ਯਾਤਰੀ ਫੀਡਬੈਕ", "@text_passenger_feedback": {"description": "Text from text_widgets: Passenger Feedback", "context": "text_widgets"}, "text_rake_deficiency_report_1": "ਰੇਕ ਕਮੀ ਰਿਪੋਰਟ", "@text_rake_deficiency_report_1": {"description": "Text from text_widgets: Rake Deficiency Report", "context": "text_widgets"}, "text_obhs_to_mcc": "OBHS ਤੋਂ MCC ਹੈਂਡਓਵਰ", "@text_obhs_to_mcc": {"description": "Text from text_widgets: OBHS to MCC Handover", "context": "text_widgets"}, "text_mcc_to_obhs": "MCC ਤੋਂ OBHS ਹੈਂਡਓਵਰ", "@text_mcc_to_obhs": {"description": "Text from text_widgets: MCC to OBHS Handover", "context": "text_widgets"}, "text_upload_data": "ਡੇਟਾ ਅਪਲੋਡ ਕਰੋ", "@text_upload_data": {"description": "Text from text_widgets: Upload data", "context": "text_widgets"}, "text_user_management": "ਉਪਭੋਗਤਾ ਪ੍ਰਬੰਧਨ", "@text_user_management": {"description": "Text from text_widgets: User Management", "context": "text_widgets"}, "text_issue_management": "ਮੁੱਦਾ ਪ੍ਰਬੰਧਨ", "@text_issue_management": {"description": "Text from text_widgets: Issue Management", "context": "text_widgets"}, "text_rail_sathi_qr": "ਰੇਲ ਸਾਥੀ QR", "@text_rail_sathi_qr": {"description": "Text from text_widgets: Rail Sathi Qr", "context": "text_widgets"}, "text_customer_care": "ਗਾਹਕ ਸੇਵਾ", "@text_customer_care": {"description": "Text from text_widgets: Customer Care", "context": "text_widgets"}, "text_change_mobile": "ਮੋਬਾਈਲ ਬਦਲੋ", "@text_change_mobile": {"description": "Profile screen translation for: text_change_mobile", "context": "profile_screen"}, "text_change_whatsapp": "ਵਟਸਐਪ ਨੰਬਰ ਬਦਲੋ", "@text_change_whatsapp": {"description": "Profile screen translation for: text_change_whatsapp", "context": "profile_screen"}, "text_alert": "ਚੇਤਾਵਨੀ", "@text_alert": {"description": "Profile screen translation for: text_alert", "context": "profile_screen"}, "text_close": "ਬੰਦ ਕਰੋ", "@text_close": {"description": "Profile screen translation for: text_close", "context": "profile_screen"}, "text_change_your_email": "ਆਪਣਾ ਈਮੇਲ ਬਦਲੋ", "@text_change_your_email": {"description": "Profile screen translation for: text_change_your_email", "context": "profile_screen"}, "text_current_email": "ਮੌਜੂਦਾ ਈਮੇਲ", "@text_current_email": {"description": "Profile screen translation for: text_current_email", "context": "profile_screen"}, "text_new_email": "ਨਵਾਂ ਈਮੇਲ", "@text_new_email": {"description": "Profile screen translation for: text_new_email", "context": "profile_screen"}, "text_please_enter_new_email": "ਕਿਰਪਾ ਕਰਕੇ ਨਵਾਂ ਈਮੇਲ ਦਰਜ ਕਰੋ", "@text_please_enter_new_email": {"description": "Profile screen translation for: text_please_enter_new_email", "context": "profile_screen"}, "text_otp": "ਓਟੀਪੀ", "@text_otp": {"description": "Profile screen translation for: text_otp", "context": "profile_screen"}, "text_resend_otp": "ਓਟੀਪੀ ਦੁਬਾਰਾ ਭੇਜੋ", "@text_resend_otp": {"description": "Profile screen translation for: text_resend_otp", "context": "profile_screen"}, "text_resend_in_seconds": "{seconds} ਸਕਿੰਟ ਵਿੱਚ ਦੁਬਾਰਾ ਭੇਜੋ", "@text_resend_in_seconds": {"description": "Profile screen translation for: text_resend_in_seconds", "context": "profile_screen"}, "text_verify_otp": "ਓਟੀਪੀ ਤਸਦੀਕ ਕਰੋ", "@text_verify_otp": {"description": "Profile screen translation for: text_verify_otp", "context": "profile_screen"}, "text_generate_otp": "ਓਟੀਪੀ ਬਣਾਓ", "@text_generate_otp": {"description": "Profile screen translation for: text_generate_otp", "context": "profile_screen"}, "text_change_your_password": "ਆਪਣਾ ਪਾਸਵਰਡ ਬਦਲੋ", "@text_change_your_password": {"description": "Profile screen translation for: text_change_your_password", "context": "profile_screen"}, "text_old_password": "ਪੁਰਾਣਾ ਪਾਸਵਰਡ", "@text_old_password": {"description": "Profile screen translation for: text_old_password", "context": "profile_screen"}, "text_new_password": "ਨਵਾਂ ਪਾਸਵਰਡ", "@text_new_password": {"description": "Profile screen translation for: text_new_password", "context": "profile_screen"}, "text_confirm_new_password": "ਨਵੇਂ ਪਾਸਵਰਡ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ", "@text_confirm_new_password": {"description": "Profile screen translation for: text_confirm_new_password", "context": "profile_screen"}, "text_please_enter_otp": "ਕਿਰਪਾ ਕਰਕੇ ਓਟੀਪੀ ਦਰਜ ਕਰੋ", "@text_please_enter_otp": {"description": "Profile screen translation for: text_please_enter_otp", "context": "profile_screen"}, "text_send_mobile_otp": "ਮੋਬਾਈਲ ਓਟੀਪੀ ਭੇਜੋ", "@text_send_mobile_otp": {"description": "Profile screen translation for: text_send_mobile_otp", "context": "profile_screen"}, "text_send_email_otp": "ਈਮੇਲ ਓਟੀਪੀ ਭੇਜੋ", "@text_send_email_otp": {"description": "Profile screen translation for: text_send_email_otp", "context": "profile_screen"}, "text_please_enter_value": "ਕਿਰਪਾ ਕਰਕੇ ਇੱਕ ਮੁੱਲ ਦਰਜ ਕਰੋ", "@text_please_enter_value": {"description": "Profile screen translation for: text_please_enter_value", "context": "profile_screen"}, "text_please_enter_valid_mobile": "ਕਿਰਪਾ ਕਰਕੇ ਇੱਕ ਵੈਧ ਮੋਬਾਈਲ ਨੰਬਰ ਦਰਜ ਕਰੋ", "@text_please_enter_valid_mobile": {"description": "Profile screen translation for: text_please_enter_valid_mobile", "context": "profile_screen"}, "text_success": "ਸਫਲਤਾ", "@text_success": {"description": "Profile screen translation for: text_success", "context": "profile_screen"}, "text_ok": "ਠੀਕ ਹੈ", "@text_ok": {"description": "Profile screen translation for: text_ok", "context": "profile_screen"}, "text_change_your_mobile_number": "ਆਪਣਾ ਮੋਬਾਈਲ ਨੰਬਰ ਬਦਲੋ", "@text_change_your_mobile_number": {"description": "Profile screen translation for: text_change_your_mobile_number", "context": "profile_screen"}, "text_current_mobile_number": "ਮੌਜੂਦਾ ਮੋਬਾਈਲ ਨੰਬਰ", "@text_current_mobile_number": {"description": "Profile screen translation for: text_current_mobile_number", "context": "profile_screen"}, "text_new_mobile_number": "ਨਵਾਂ ਮੋਬਾਈਲ ਨੰਬਰ", "@text_new_mobile_number": {"description": "Profile screen translation for: text_new_mobile_number", "context": "profile_screen"}, "text_please_enter_new_mobile": "ਕਿਰਪਾ ਕਰਕੇ ਨਵਾਂ ਮੋਬਾਈਲ ਨੰਬਰ ਦਰਜ ਕਰੋ", "@text_please_enter_new_mobile": {"description": "Profile screen translation for: text_please_enter_new_mobile", "context": "profile_screen"}, "text_change_your_whatsapp_number": "ਆਪਣਾ ਵਟਸਐਪ ਨੰਬਰ ਬਦਲੋ", "@text_change_your_whatsapp_number": {"description": "Profile screen translation for: text_change_your_whatsapp_number", "context": "profile_screen"}, "text_current_whatsapp_number": "ਮੌਜੂਦਾ ਵਟਸਐਪ ਨੰਬਰ", "@text_current_whatsapp_number": {"description": "Profile screen translation for: text_current_whatsapp_number", "context": "profile_screen"}, "text_new_whatsapp_number": "ਨਵਾਂ ਵਟਸਐਪ ਨੰਬਰ", "@text_new_whatsapp_number": {"description": "Profile screen translation for: text_new_whatsapp_number", "context": "profile_screen"}, "text_please_enter_new_whatsapp": "ਕਿਰਪਾ ਕਰਕੇ ਨਵਾਂ ਵਟਸਐਪ ਮੋਬਾਈਲ ਨੰਬਰ ਦਰਜ ਕਰੋ", "@text_please_enter_new_whatsapp": {"description": "Profile screen translation for: text_please_enter_new_whatsapp", "context": "profile_screen"}, "text_train": "ਰੇਲ", "@text_train": {"description": "Table column header for train", "context": "add_train_screen"}, "text_coaches": "ਕੋਚ", "@text_coaches": {"description": "Table column header for coaches", "context": "add_train_screen"}, "text_origin_date": "ਮੂਲ ਤਾਰੀਖ", "@text_origin_date": {"description": "Table column header for date", "context": "add_train_screen"}, "text_na": "ਉਪਲਬਧ ਨਹੀਂ", "@text_na": {"description": "Table column header for na", "context": "add_train_screen"}, "text_send_otp": "ਓਟੀਪੀ ਭੇਜੋ", "@text_send_otp": {"description": "Send OTP button text", "context": "change_email_modal"}, "text_failed_to_send_otp": "ਓਟੀਪੀ ਭੇਜਣ ਵਿੱਚ ਅਸਫਲ: {error}", "@text_failed_to_send_otp": {"description": "Error message when OTP sending fails", "context": "change_email_modal", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "text_email_saved_successfully": "ਈਮੇਲ ਸਫਲਤਾਪੂਰਵਕ ਸੇਵ ਹੋ ਗਿਆ!", "@text_email_saved_successfully": {"description": "Success message when email is saved", "context": "change_email_modal"}, "text_failed_to_verify_otp": "ਓਟੀਪੀ ਤਸਦੀਕ ਕਰਨ ਵਿੱਚ ਅਸਫਲ: {error}", "@text_failed_to_verify_otp": {"description": "Error message when OTP verification fails", "context": "change_email_modal", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "text_cancel": "ਰੱਦ ਕਰੋ", "@text_cancel": {"description": "Cancel button text", "context": "general"}, "rm_feedback_app_bar_title": "ਰੇਲਮਦਦ ਯਾਤਰੀ ਫੀਡਬੈਕ", "rm_feedback_main_title": "ਰੇਲਮਦਦ ਫੀਡਬੈਕ", "form_pnr_number": "PNR ਨੰਬਰ *", "form_crn_number": "CRN ਨੰਬਰ*", "form_train_no": "ਰੇਲ ਨੰਬਰ *", "form_train_name": "ਰੇਲ ਦਾ ਨਾਮ *", "form_passenger_name": "ਯਾਤਰੀ ਦਾ ਨਾਮ *", "form_coach_no": "ਕੋਚ ਨੰਬਰ *", "form_berth_no": "ਬਰਥ ਨੰਬਰ *", "form_mobile_number": "ਮੋਬਾਈਲ ਨੰਬਰ", "form_email_id": "ਈਮੇਲ ਆਈਡੀ", "form_issue_type": "ਸਮੱਸਿਆ ਦੀ ਕਿਸਮ", "form_sub_issue_type": "ਉਪ ਸਮੱਸਿਆ ਦੀ ਕਿਸਮ", "form_resolved_status": "ਹੱਲ ਹੋ ਗਿਆ (ਹਾਂ/ਨਹੀਂ) *", "form_marks": "ਅੰਕ (1 ਤੋਂ 10) *", "form_task_status": "ਕੰਮ ਦੀ ਸਥਿਤੀ *", "form_remarks": "ਯਾਤਰੀ ਦੀ ਟਿੱਪਣੀ", "btn_validate": "ਤਸਦੀਕ ਕਰੋ", "btn_verified": "ਤਸਦੀਕ ਸ਼ੁਦਾ", "btn_verify_email": "ਈਮੇਲ ਤਸਦੀਕ ਕਰੋ", "btn_verify_otp": "OTP ਤਸਦੀਕ ਕਰੋ", "btn_submit_feedback": "ਫੀਡਬੈਕ ਜਮ੍ਹਾਂ ਕਰੋ", "btn_upload_pnr_image": "PNR ਤਸਵੀਰ ਅਪਲੋਡ ਕਰੋ", "btn_pick_media": "ਫੀਡਬੈਕ ਲਈ ਤਸਵੀਰਾਂ/ਵੀਡੀਓ ਚੁਣੋ", "btn_camera": "ਕੈਮਰਾ", "btn_gallery": "ਗੈਲਰੀ", "btn_image": "ਤਸਵੀਰ", "btn_video": "ਵੀਡੀਓ", "btn_i_understand": "ਮੈਂ ਸਮਝ ਗਿਆ", "btn_ok": "ਠੀਕ ਹੈ", "status_verified": "ਤਸਦੀਕ ਸ਼ੁਦਾ", "status_pending": "ਬਕਾਇਆ", "status_completed": "ਪੂਰਾ", "status_yes": "ਹਾਂ", "status_no": "ਨਹੀਂ", "status_select": "ਚੁਣੋ", "section_email_verification": "ਈਮੇਲ ਤਸਦੀਕ (ਵਿਕਲਪਿਕ)", "section_selected_images": "ਚੁਣੀਆਂ ਤਸਵੀਰਾਂ:", "section_selected_videos": "ਚੁਣੇ ਵੀਡੀਓ:", "dialog_email_verification_info": "ਈਮੇਲ ਤਸਦੀਕ ਜਾਣਕਾਰੀ", "dialog_select_media_type": "ਮੀਡੀਆ ਕਿਸਮ ਚੁਣੋ", "validation_fill_all_fields": "ਕਿਰਪਾ ਕਰਕੇ ਸਾਰੇ ਖੇਤਰ ਵੈਧ ਜਾਣਕਾਰੀ ਨਾਲ ਭਰੋ।", "validation_pnr_digits": "PNR ਨੰਬਰ 8 ਜਾਂ 10 ਅੰਕਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "validation_berth_number": "ਬਰਥ ਨੰਬਰ ਇੱਕ ਵੈਧ ਸੰਖਿਆ ਹੋਣੀ ਚਾਹੀਦੀ ਹੈ", "validation_feedback_length": "ਫੀਡਬੈਕ 100 ਅੱਖਰਾਂ ਤੋਂ ਵੱਧ ਨਹੀਂ ਹੋ ਸਕਦਾ", "validation_email_required": "ਕਿਰਪਾ ਕਰਕੇ ਇੱਕ ਵੈਧ ਈਮੇਲ ਆਈਡੀ ਦਰਜ ਕਰੋ।", "validation_otp_required": "ਕਿਰਪਾ ਕਰਕੇ OTP ਦਰਜ ਕਰੋ।", "validation_train_no_required": "ਰੇਲ ਨੰਬਰ ਲੋੜੀਂਦਾ ਹੈ", "validation_train_name_required": "ਰੇਲ ਦਾ ਨਾਮ ਲੋੜੀਂਦਾ ਹੈ", "validation_passenger_name_required": "ਯਾਤਰੀ ਦਾ ਨਾਮ ਲੋੜੀਂਦਾ ਹੈ", "validation_mobile_required": "ਮੋਬਾਈਲ ਨੰਬਰ ਲੋੜੀਂਦਾ ਹੈ", "validation_mobile_digits": "ਮੋਬਾਈਲ ਨੰਬਰ 10 ਅੰਕਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "validation_issue_type_required": "ਕਿਰਪਾ ਕਰਕੇ ਇੱਕ ਸਮੱਸਿਆ ਦੀ ਕਿਸਮ ਚੁਣੋ", "validation_sub_issue_required": "ਕਿਰਪਾ ਕਰਕੇ ਇੱਕ ਉਪ-ਸਮੱਸਿਆ ਦੀ ਕਿਸਮ ਚੁਣੋ", "validation_resolved_required": "ਕਿਰਪਾ ਕਰਕੇ ਹੱਲ ਦੀ ਸਥਿਤੀ ਚੁਣੋ", "validation_marks_required": "ਕਿਰਪਾ ਕਰਕੇ ਅੰਕ ਚੁਣੋ", "msg_pnr_images_limit": "ਤੁਸੀਂ ਸਿਰਫ਼ 3 PNR ਤਸਵੀਰਾਂ ਚੁਣ ਸਕਦੇ ਹੋ", "msg_feedback_images_limit": "ਵੱਧ ਤੋਂ ਵੱਧ 3 ਫੀਡਬੈਕ ਤਸਵੀਰਾਂ ਦੀ ਇਜਾਜ਼ਤ ਹੈ", "msg_images_added_limit": "ਸਿਰਫ਼ {count} ਤਸਵੀਰਾਂ ਜੋੜੀਆਂ ਗਈਆਂ। ਵੱਧ ਤੋਂ ਵੱਧ 3 ਦੀ ਸੀਮਾ ਪਹੁੰਚ ਗਈ।", "msg_error_picking_media": "ਮੀਡੀਆ ਚੁਣਨ ਵਿੱਚ ਗਲਤੀ: {error}", "msg_failed_fetch_train_name": "ਰੇਲ ਦਾ ਨਾਮ ਲਿਆਉਣ ਵਿੱਚ ਅਸਫਲ", "msg_invalid_pnr": "ਗਲਤ PNR ਨੰਬਰ।", "msg_pnr_success": "PNR ਵੇਰਵੇ ਸਫਲਤਾਪੂਰਵਕ ਲਿਆਏ ਗਏ।", "msg_pnr_validation_failed": "PNR ਵੇਰਵੇ ਤਸਦੀਕ ਕਰਨ ਵਿੱਚ ਅਸਫਲ। ਗਲਤ PNR ਨੰਬਰ।", "msg_email_verification_sent": "ਈਮੇਲ ਤਸਦੀਕ ਸ਼ੁਰੂ ਕੀਤੀ ਗਈ। ਕਿਰਪਾ ਕਰਕੇ ਆਪਣੇ ਇਨਬਾਕਸ ਅਤੇ ਸਪੈਮ ਫੋਲਡਰ ਦੋਵਾਂ ਦੀ ਜਾਂਚ ਕਰੋ।।", "msg_otp_verified": "OTP ਸਫਲਤਾਪੂਰਵਕ ਤਸਦੀਕ ਕੀਤਾ ਗਿਆ।", "msg_feedback_submitted": "ਫੀਡਬੈਕ ਸਫਲਤਾਪੂਰਵਕ ਜਮ੍ਹਾਂ ਕੀਤਾ ਗਿਆ!", "msg_feedback_failed": "ਫੀਡਬੈਕ ਜਮ੍ਹਾਂ ਕਰਨ ਵਿੱਚ ਅਸਫਲ", "msg_unexpected_error": "ਇੱਕ ਅਣਕਿਆਸੀ ਗਲਤੀ ਹੋਈ। ਕਿਰਪਾ ਕਰਕੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "info_spam_folder_note": "ਕਿਰਪਾ ਕਰਕੇ ਨੋਟ ਕਰੋ ਕਿ ਤਸਦੀਕੀ ਈਮੇਲਾਂ ਕਦੇ-ਕਦੇ ਤੁਹਾਡੇ ਸਪੈਮ/ਜੰਕ ਫੋਲਡਰ ਵਿੱਚ ਪਹੁੰਚ ਸਕਦੀਆਂ ਹਨ।", "info_after_requesting_otp": "OTP ਦੀ ਬੇਨਤੀ ਕਰਨ ਤੋਂ ਬਾਅਦ:", "info_check_inbox": "ਪਹਿਲਾਂ ਆਪਣਾ ਇਨਬਾਕਸ ਚੈੱਕ ਕਰੋ", "info_check_spam": "ਜੇ ਨਹੀਂ ਮਿਲਿਆ, ਤਾਂ ਸਪੈਮ/ਜੰਕ ਫੋਲਡਰ ਚੈੱਕ ਕਰੋ", "info_add_safe_sender": "ਸਾਡੇ ਡੋਮੇਨ ਨੂੰ ਆਪਣੀ ਸੁਰੱਖਿਤ ਭੇਜਣ ਵਾਲੀ ਸੂਚੀ ਵਿੱਚ ਸ਼ਾਮਲ ਕਰੋ", "text_no_feedback_images": "ਕੋਈ ਫੀਡਬੈਕ ਤਸਵੀਰਾਂ ਚੁਣੀਆਂ ਨਹੀਂ", "text_no_pnr_images": "ਕੋਈ PNR ਤਸਵੀਰਾਂ ਚੁਣੀਆਂ ਨਹੀਂ", "text_character_count": "{count}/100 ਅੱਖਰ", "loading_sending_otp": "OTP ਭੇਜਿਆ ਜਾ ਰਿਹਾ ਹੈ", "loading_verifying_otp": "OTP ਤਸਦੀਕ ਕੀਤਾ ਜਾ ਰਿਹਾ ਹੈ", "loading_submitting_feedback": "ਫੀਡਬੈਕ ਜਮ੍ਹਾਂ ਕੀਤਾ ਜਾ ਰਿਹਾ ਹੈ", "attendance_api_summary": "API ਸਾਰ ਵੇਰਵੇ", "@attendance_api_summary": {"description": "Attendance: API Summary Details", "context": "attendance"}, "attendance_assigned_coaches": "🚆 ਨਿਰਧਾਰਿਤ Coaches:", "@attendance_assigned_coaches": {"description": "Attendance: 🚆 Assigned Coaches:", "context": "attendance"}, "attendance_available_label": "ਉਪਲਬਧ: {count}", "@attendance_available_label": {"description": "Attendance: Available: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_available_section": "ਉਪਲਬਧ", "@attendance_available_section": {"description": "Attendance: Available", "context": "attendance"}, "attendance_berths_count": "{count} berths", "@attendance_berths_count": {"description": "Attendance: {count} berths", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_buffer_time_restriction": "The ਸਮਾਂ between the current ਸਮਾਂ and the ਟਰੇਨ's arrival ਸਮਾਂ is not within the 2-hour buffer.", "@attendance_buffer_time_restriction": {"description": "Attendance: The time between the current time and the train's arrival time is not within the 2-hour buffer.", "context": "attendance"}, "attendance_cancel": "ਰੱਦ ਕਰੋ", "@attendance_cancel": {"description": "Attendance: Cancel", "context": "attendance"}, "attendance_cancelled": "Cancelled", "@attendance_cancelled": {"description": "Attendance: Cancelled", "context": "attendance"}, "attendance_chart_not_prepared": "ਚਾਰਟ has not been prepared for this ਸਟੇਸ਼ਨ", "@attendance_chart_not_prepared": {"description": "Attendance: Chart has not been prepared for this station", "context": "attendance"}, "attendance_charting_refreshed": "ਚਾਰਟਿੰਗ refreshed at: {time}", "@attendance_charting_refreshed": {"description": "Attendance: Charting refreshed at: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_charting_started": "ਚਾਰਟਿੰਗ started at: {time}", "@attendance_charting_started": {"description": "Attendance: Charting started at: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_charting_time": "ਚਾਰਟਿੰਗ ਸਮਾਂ: {time}", "@attendance_charting_time": {"description": "Attendance: Charting Time: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_click_more": "click for more...", "@attendance_click_more": {"description": "Attendance: click for more...", "context": "attendance"}, "attendance_coach_label": "🚃 ਕੋਚ: {coach}", "@attendance_coach_label": {"description": "Attendance: 🚃 Coach: {coach}", "context": "attendance", "placeholders": {"coach": {"type": "String"}}}, "attendance_coach_occupancy": "ਕੋਚ Occupancy ਵੇਰਵੇ", "@attendance_coach_occupancy": {"description": "Attendance: Coach Occupancy <PERSON>", "context": "attendance"}, "attendance_coach_type": "ਕੋਚ Type:", "@attendance_coach_type": {"description": "Attendance: Coach Type:", "context": "attendance"}, "attendance_coaches": "Coaches: {coaches}", "@attendance_coaches": {"description": "Attendance: Coaches: {coaches}", "context": "attendance", "placeholders": {"coaches": {"type": "String"}}}, "attendance_concise_view": "Concise ਵੇਖੋ", "@attendance_concise_view": {"description": "Attendance: Concise View", "context": "attendance"}, "attendance_count_label": "A: {count}", "@attendance_count_label": {"description": "Attendance: A: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_daily": "Daily", "@attendance_daily": {"description": "Attendance: Daily", "context": "attendance"}, "attendance_data_refreshed": "ਡਾਟਾ refreshed ਸਫਲਤਾਪੂਰਵਕ", "@attendance_data_refreshed": {"description": "Attendance: Data refreshed successfully", "context": "attendance"}, "attendance_deboarding": "🔴 ਉਤਰਨਾ:", "@attendance_deboarding": {"description": "Attendance: 🔴 Deboarding:", "context": "attendance"}, "attendance_deboarding_none": "🔴 ਉਤਰਨਾ: None", "@attendance_deboarding_none": {"description": "Attendance: 🔴 Deboarding: None", "context": "attendance"}, "attendance_detailed_view": "Detailed ਵੇਖੋ", "@attendance_detailed_view": {"description": "Attendance: Detailed View", "context": "attendance"}, "attendance_ehk_assigned": "EHK ਨਿਰਧਾਰਿਤ for ਟਰੇਨ:{ehkName}", "@attendance_ehk_assigned": {"description": "Attendance: EHK Assigned for train:{ehkName}", "context": "attendance", "placeholders": {"ehkName": {"type": "String"}}}, "attendance_expected_charting": "Expected ਚਾਰਟਿੰਗ ਸਮਾਂ: {time}", "@attendance_expected_charting": {"description": "Attendance: Expected Charting Time: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_failed_load_data": "ਅਸਫਲ to load detailed ਡਾਟਾ: {error}", "@attendance_failed_load_data": {"description": "Attendance: Failed to load detailed data: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_failed_update_status": "ਅਸਫਲ to ਅਪਡੇਟ ਕਰੋ ਟਰੇਨ ਸਥਿਤੀ", "@attendance_failed_update_status": {"description": "Attendance: Failed to update train status", "context": "attendance"}, "attendance_go": "ਜਾਓ", "@attendance_go": {"description": "Attendance: Go", "context": "attendance"}, "attendance_in_route": "In-route", "@attendance_in_route": {"description": "Attendance: In-route", "context": "attendance"}, "attendance_inside": "ਅੰਦਰ", "@attendance_inside": {"description": "Attendance: Inside", "context": "attendance"}, "attendance_inside_train": "You are now ਮਾਰਕ ਕੀਤਾ as ਅੰਦਰ the ਟਰੇਨ", "@attendance_inside_train": {"description": "Attendance: You are now marked as inside the train", "context": "attendance"}, "attendance_journey_status_updated": "ਸਫ਼ਰ ਸਥਿਤੀ updated to {status}", "@attendance_journey_status_updated": {"description": "Attendance: Journey status updated to {status}", "context": "attendance", "placeholders": {"status": {"type": "String"}}}, "attendance_last_fetched": "Last fetched: {time}", "@attendance_last_fetched": {"description": "Attendance: Last fetched: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_loading": "ਲੋਡ ਹੋ ਰਿਹਾ ਹੈ...", "@attendance_loading": {"description": "Attendance: Loading...", "context": "attendance"}, "attendance_location_not_fetched": "ਟਰੇਨ ਸਥਾਨ is not fetched yet, ਮਿਹਰਬਾਨੀ ਕਰਕੇ try again later", "@attendance_location_not_fetched": {"description": "Attendance: Train Location is not fetched yet, please try again later", "context": "attendance"}, "attendance_na": "N/A", "@attendance_na": {"description": "Attendance: N/A", "context": "attendance"}, "attendance_near_stations": "You're near the following ਸਟੇਸ਼ਨ(s):", "@attendance_near_stations": {"description": "Attendance: You're near the following station(s):", "context": "attendance"}, "attendance_nearby_station_alert": "🛤️ ਨੇੜਲੇ ਸਟੇਸ਼ਨ ਚੇਤਾਵਨੀ", "@attendance_nearby_station_alert": {"description": "Attendance: 🛤️ Nearby Station Alert", "context": "attendance"}, "attendance_non_sleeper": "Non-Sleeper", "@attendance_non_sleeper": {"description": "Attendance: Non-Sleeper", "context": "attendance"}, "attendance_not_attendance_station": "ਹਾਜ਼ਰੀ cannot be ਮਾਰਕ ਕੀਤਾ for ਸਟੇਸ਼ਨ {stationCode} as it is not an ਹਾਜ਼ਰੀ ਸਟੇਸ਼ਨ.", "@attendance_not_attendance_station": {"description": "Attendance: Attendance cannot be marked for station {stationCode} as it is not an attendance station.", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_not_inside_train": "You are not ਅੰਦਰ the ਟਰੇਨ. ਮਿਹਰਬਾਨੀ ਕਰਕੇ ਜਾਓ ਅੰਦਰ the ਟਰੇਨ first.", "@attendance_not_inside_train": {"description": "Attendance: You are not inside the train. Please go inside the train first.", "context": "attendance"}, "attendance_off_label": "Off: {count}", "@attendance_off_label": {"description": "Attendance: Off: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_offboarding_section": "Offboarding", "@attendance_offboarding_section": {"description": "Attendance: Offboarding", "context": "attendance"}, "attendance_ok": "ਠੀਕ", "@attendance_ok": {"description": "Attendance: OK", "context": "attendance"}, "attendance_on_label": "On: {count}", "@attendance_on_label": {"description": "Attendance: On: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_onboarding": "🟢 ਚੜ੍ਹਨਾ:", "@attendance_onboarding": {"description": "Attendance: 🟢 Onboarding:", "context": "attendance"}, "attendance_onboarding_none": "🟢 ਚੜ੍ਹਨਾ: None", "@attendance_onboarding_none": {"description": "Attendance: 🟢 Onboarding: None", "context": "attendance"}, "attendance_onboarding_section": "ਚੜ੍ਹਨਾ", "@attendance_onboarding_section": {"description": "Attendance: Onboarding", "context": "attendance"}, "attendance_other_ca": "Other CA", "@attendance_other_ca": {"description": "Attendance: Other CA", "context": "attendance"}, "attendance_other_ehk": "Other EHK/OBHS", "@attendance_other_ehk": {"description": "Attendance: Other EHK/OBHS", "context": "attendance"}, "attendance_outside_train": "You are now ਮਾਰਕ ਕੀਤਾ as ਬਾਹਰ the ਟਰੇਨ", "@attendance_outside_train": {"description": "Attendance: You are now marked as outside the train", "context": "attendance"}, "attendance_passenger_chart": "ਮੁਸਾਫਿਰ ਚਾਰਟ   Atten..", "@attendance_passenger_chart": {"description": "Attendance: Passenger Chart   Atten..", "context": "attendance"}, "attendance_refresh_failed": "ਰਿਫਰੈਸ਼ ਅਸਫਲ: {error}", "@attendance_refresh_failed": {"description": "Attendance: Refresh failed: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_screen_title": "ਹਾਜ਼ਰੀ", "@attendance_screen_title": {"description": "Attendance: Attendance", "context": "attendance"}, "attendance_select_date": "ਚੁਣੋ ਤਾਰੀਖ", "@attendance_select_date": {"description": "Attendance: Select date", "context": "attendance"}, "attendance_select_train_date": "ਮਿਹਰਬਾਨੀ ਕਰਕੇ ਚੁਣੋ a ਟਰੇਨ ਨੰਬਰ and ਤਾਰੀਖ.", "@attendance_select_train_date": {"description": "Attendance: Please select a train number and date.", "context": "attendance"}, "attendance_select_train_first": "ਮਿਹਰਬਾਨੀ ਕਰਕੇ ਚੁਣੋ a ਟਰੇਨ first", "@attendance_select_train_first": {"description": "Attendance: Please select a train first", "context": "attendance"}, "attendance_self": "Self", "@attendance_self": {"description": "Attendance: Self", "context": "attendance"}, "attendance_sleeper": "Sleeper", "@attendance_sleeper": {"description": "Attendance: Sleeper", "context": "attendance"}, "attendance_station_details": "ਸਟੇਸ਼ਨ ਵੇਰਵੇ - {stationCode}", "@attendance_station_details": {"description": "Attendance: Station Details - {stationCode}", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_stoppages": "Stoppages", "@attendance_stoppages": {"description": "Attendance: Stoppages", "context": "attendance"}, "attendance_timings": "Tim<PERSON>", "@attendance_timings": {"description": "Attendance: Timings", "context": "attendance"}, "attendance_today": "Today", "@attendance_today": {"description": "Attendance: Today", "context": "attendance"}, "attendance_too_far_from_station": "You're over 50 KM away from the selected ਸਟੇਸ਼ਨ {stationCode}. ਹਾਜ਼ਰੀ can only be ਮਾਰਕ ਕੀਤਾ when you're within the allowed range.", "@attendance_too_far_from_station": {"description": "Attendance: You're over 50 KM away from the selected station {stationCode}. Attendance can only be marked when you're within the allowed range.", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_train": "ਟਰੇਨ", "@attendance_train": {"description": "Attendance: Train", "context": "attendance"}, "attendance_train_date": "ਟਰੇਨ {trainNo} - {date}", "@attendance_train_date": {"description": "Attendance: Train {trainNo} - {date}", "context": "attendance", "placeholders": {"trainNo": {"type": "String"}, "date": {"type": "String"}}}, "attendance_train_depot": "ਟਰੇਨ ਡਿਪੋ:{depot}", "@attendance_train_depot": {"description": "Attendance: Train Depot:{depot}", "context": "attendance", "placeholders": {"depot": {"type": "String"}}}, "attendance_train_not_running": "ਟਰੇਨ Not Running", "@attendance_train_not_running": {"description": "Attendance: Train Not Running", "context": "attendance"}, "attendance_train_not_running_message": "ਟਰੇਨ {trainNumber} is NOT running on {dayOfWeek}", "@attendance_train_not_running_message": {"description": "Attendance: Train {trainNumber} is NOT running on {dayOfWeek}", "context": "attendance", "placeholders": {"trainNumber": {"type": "String"}, "dayOfWeek": {"type": "String"}}}, "attendance_update": "ਅਪਡੇਟ ਕਰੋ", "@attendance_update": {"description": "Attendance: Update", "context": "attendance"}, "attendance_update_journey_status": "ਅਪਡੇਟ ਕਰੋ ਸਫ਼ਰ ਸਥਿਤੀ", "@attendance_update_journey_status": {"description": "Attendance: Update Journey Status", "context": "attendance"}, "attendance_update_message": "A new ਵਰਜ਼ਨ of the app is ਉਪਲਬਧ. You must ਅਪਡੇਟ ਕਰੋ to continue using the app.", "@attendance_update_message": {"description": "Attendance: A new version of the app is available. You must update to continue using the app.", "context": "attendance"}, "attendance_update_now": "ਅਪਡੇਟ ਕਰੋ Now", "@attendance_update_now": {"description": "Attendance: Update Now", "context": "attendance"}, "attendance_update_required": "ਅਪਡੇਟ ਕਰੋ ਲੋੜੀਂਦਾ", "@attendance_update_required": {"description": "Attendance: Update Required", "context": "attendance"}, "attendance_user_location": "ਵਰਤੋਂਕਾਰ ਸਥਾਨ:{locationStatus}", "@attendance_user_location": {"description": "Attendance: User Location:{locationStatus}", "context": "attendance", "placeholders": {"locationStatus": {"type": "String"}}}, "attendance_details_distance": "ਦੂਰੀ: {distance} km", "@attendance_details_distance": {"description": "Attendance: Distance: {distance} km", "context": "attendance", "placeholders": {"distance": {"type": "String"}}}, "attendance_details_error": "ਗਲਤੀ: {error}", "@attendance_details_error": {"description": "Attendance: Error: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_details_journey_date": "ਸਫ਼ਰ ਤਾਰੀਖ:", "@attendance_details_journey_date": {"description": "Attendance: Journey Date:", "context": "attendance"}, "attendance_details_latitude": "latitude: {latitude}", "@attendance_details_latitude": {"description": "Attendance: latitude: {latitude}", "context": "attendance", "placeholders": {"latitude": {"type": "String"}}}, "attendance_details_longitude": "longitude: {longitude}", "@attendance_details_longitude": {"description": "Attendance: longitude: {longitude}", "context": "attendance", "placeholders": {"longitude": {"type": "String"}}}, "attendance_details_match_percentage": "Match ਪ੍ਰਤੀਸ਼ਤ: {percentage}", "@attendance_details_match_percentage": {"description": "Attendance: Match Percentage: {percentage}", "context": "attendance", "placeholders": {"percentage": {"type": "String"}}}, "attendance_details_nearest_station": "nearest ਸਟੇਸ਼ਨ: {station}", "@attendance_details_nearest_station": {"description": "Attendance: nearest Station: {station}", "context": "attendance", "placeholders": {"station": {"type": "String"}}}, "attendance_details_no_attendance": "No ਹਾਜ਼ਰੀ found.", "@attendance_details_no_attendance": {"description": "Attendance: No attendance found.", "context": "attendance"}, "attendance_details_no_data": "No ਡਾਟਾ ਉਪਲਬਧ.", "@attendance_details_no_data": {"description": "Attendance: No data available.", "context": "attendance"}, "attendance_details_no_human_detected": "No human detected", "@attendance_details_no_human_detected": {"description": "Attendance: No human detected", "context": "attendance"}, "attendance_details_station_code": "ਸਟੇਸ਼ਨ Code:", "@attendance_details_station_code": {"description": "Attendance: Station Code:", "context": "attendance"}, "attendance_details_status": "ਸਥਿਤੀ: {status}", "@attendance_details_status": {"description": "Attendance: Status: {status}", "context": "attendance", "placeholders": {"status": {"type": "String"}}}, "attendance_details_status_marked": "ਮਾਰਕ ਕੀਤਾ", "@attendance_details_status_marked": {"description": "Attendance: Marked", "context": "attendance"}, "attendance_details_status_pending": "Pending", "@attendance_details_status_pending": {"description": "Attendance: Pending", "context": "attendance"}, "attendance_details_title": "ਹਾਜ਼ਰੀ ਵੇਰਵੇ", "@attendance_details_title": {"description": "Attendance: Attendance Details", "context": "attendance"}, "attendance_details_train_number": "ਟਰੇਨ ਨੰਬਰ:", "@attendance_details_train_number": {"description": "Attendance: Train Number:", "context": "attendance"}, "attendance_details_updated": "All ਵੇਰਵੇ updated ਸਫਲਤਾਪੂਰਵਕ.", "@attendance_details_updated": {"description": "Attendance: All details updated successfully.", "context": "attendance"}, "attendance_details_updated_at": "Updated At: {updatedAt}", "@attendance_details_updated_at": {"description": "Attendance: Updated At: {updatedAt}", "context": "attendance", "placeholders": {"updatedAt": {"type": "String"}}}, "attendance_details_updated_by": "Updated By: {updatedBy}", "@attendance_details_updated_by": {"description": "Attendance: Updated By: {updatedBy}", "context": "attendance", "placeholders": {"updatedBy": {"type": "String"}}}, "attendance_details_username": "Username: {username}", "@attendance_details_username": {"description": "Attendance: Username: {username}", "context": "attendance", "placeholders": {"username": {"type": "String"}}}, "btn_no_attendance_found": "No ਹਾਜ਼ਰੀ found.", "@btn_no_attendance_found": {"description": "Attendance: No attendance found.", "context": "attendance"}, "text_attendance_already_submitted": "ਹਾਜ਼ਰੀ Already Submitted", "@text_attendance_already_submitted": {"description": "Attendance: Attendance Already Submitted", "context": "attendance"}, "text_attendance_marked_successfully": "ਹਾਜ਼ਰੀ ਮਾਰਕ ਕੀਤਾ ਸਫਲਤਾਪੂਰਵਕ!", "@text_attendance_marked_successfully": {"description": "Attendance: Attendance marked successfully!", "context": "attendance"}, "text_no_attendance_found": "No ਹਾਜ਼ਰੀ found.", "@text_no_attendance_found": {"description": "Attendance: No attendance found.", "context": "attendance"}, "text_welcome_to_railops": "ਰੇਲਓਪਸ ਵਿੱਚ ਤੁਹਾਡਾ ਸਵਾਗਤ ਹੈ", "@text_welcome_to_railops": {"description": "Welcome message on login screen", "context": "login_screen"}, "text_sign_up_to_railops": "ਰੇਲਓਪਸ ਵਿੱਚ ਸਾਈਨ ਅਪ ਕਰੋ", "@text_sign_up_to_railops": {"description": "Sign up screen title", "context": "sign_up_screen"}, "text_login_using_fingerprint": "ਫਿੰਗਰਪ੍ਰਿੰਟ ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਲਾਗਇਨ ਕਰੋ", "@text_login_using_fingerprint": {"description": "Biometric authentication prompt", "context": "authentication"}, "text_logging_in_please_wait": "ਲਾਗਇਨ ਹੋ ਰਿਹਾ ਹੈ... ਕਿਰਪਾ ਕਰਕੇ ਇੰਤਜ਼ਾਰ ਕਰੋ।", "@text_logging_in_please_wait": {"description": "Loading message during login", "context": "authentication"}, "text_log_in": "ਲਾਗ ਇਨ", "@text_log_in": {"description": "Login button text", "context": "authentication"}, "text_home": "ਘਰ", "@text_home": {"description": "Home navigation title", "context": "navigation"}, "text_home_screen": "ਹੋਮ ਸਕ੍ਰੀਨ", "@text_home_screen": {"description": "Home screen title text", "context": "home_screen"}, "@form_mobile_number": {"description": "Mobile number field label", "context": "login_form"}, "form_password": "ਪਾਸਵਰਡ *", "@form_password": {"description": "Password field label", "context": "login_form"}, "error_enter_mobile_number": "ਕਿਰਪਾ ਕਰਕੇ ਆਪਣਾ ਮੋਬਾਈਲ ਨੰਬਰ ਦਰਜ ਕਰੋ", "@error_enter_mobile_number": {"description": "Mobile number validation error", "context": "login_form"}, "error_mobile_number_digits": "ਮੋਬਾਈਲ ਨੰਬਰ 10 ਅੰਕਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "@error_mobile_number_digits": {"description": "Mobile number length validation error", "context": "login_form"}, "error_enter_password": "ਕਿਰਪਾ ਕਰਕੇ ਆਪਣਾ ਪਾਸਵਰਡ ਦਰਜ ਕਰੋ", "@error_enter_password": {"description": "Password validation error", "context": "login_form"}, "btn_log_in_with_mobile": "ਮੋਬਾਈਲ ਨੰਬਰ ਨਾਲ ਲਾਗ ਇਨ ਕਰੋ", "@btn_log_in_with_mobile": {"description": "Mobile login button text", "context": "login_form"}, "btn_new_user_sign_up": "ਨਵਾਂ ਉਪਭੋਗਤਾ? ਇੱਥੇ ਸਾਈਨ ਅਪ ਕਰੋ", "@btn_new_user_sign_up": {"description": "Sign up button text", "context": "login_form"}, "text_privacy_policy": "ਗੋਪਨੀਯਤਾ ਨੀਤੀ", "@text_privacy_policy": {"description": "Privacy policy link text", "context": "login_form"}, "text_terms_conditions": "ਨਿਯਮ ਅਤੇ ਸ਼ਰਤਾਂ", "@text_terms_conditions": {"description": "Terms and conditions link text", "context": "login_form"}, "text_know_about_app": "ਇਸ ਐਪ ਬਾਰੇ ਜਾਣੋ", "@text_know_about_app": {"description": "App information link text", "context": "login_form"}, "msg_login_successful": "ਲਾਗਇਨ ਸਫਲ", "@msg_login_successful": {"description": "Login success message", "context": "authentication"}, "msg_invalid_pin": "ਗਲਤ ਪਿੰਨ", "@msg_invalid_pin": {"description": "Invalid PIN error message", "context": "authentication"}}