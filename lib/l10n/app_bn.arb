{"@@locale": "bn", "appTitle": "রেলঅপ্স", "@appTitle": {"description": "The title of the application"}, "cancel": "", "confirm": "", "delete": "", "edit": "", "error": "", "home": "", "loading": "", "login": "লগিন", "@login": {"description": "Login button text"}, "logout": "", "next": "", "no": "", "ok": "", "password": "", "previous": "", "refresh": "", "retry": "", "save": "", "search": "", "settings": "", "submit": "", "username": "", "welcome": "রেলঅপ্স-এ আপনাকে স্বাগতম", "@welcome": {"description": "Welcome message displayed on home screen"}, "yes": "", "test_welcome": "স্বাগতম", "@test_welcome": {"description": "Welcome message for testing", "context": "test_context"}, "test_login": "লগইন", "@test_login": {"description": "Login button text", "context": "button_labels"}, "text_menu": "মেনু", "@text_menu": {"description": "Text for drawer menu header", "context": "custom_drawer"}, "text_train_tracker": "ট্রেন ট্র্যাকার", "@text_train_tracker": {"description": "Text from text_widgets: Train Tracker", "context": "text_widgets"}, "text_assign_ca": "CA নিয়োগ করুন", "@text_assign_ca": {"description": "Text from text_widgets: Assign CA", "context": "text_widgets"}, "text_assign_cs": "CS নিয়োগ করুন", "@text_assign_cs": {"description": "Text from text_widgets: Assign CS", "context": "text_widgets"}, "text_pnr_details": "PNR বিবরণ", "@text_pnr_details": {"description": "Text from text_widgets: PNR Details", "context": "text_widgets"}, "text_passenger_chart": "যাত্রী চার্ট", "@text_passenger_chart": {"description": "Text from text_widgets: Passenger Chart", "context": "text_widgets"}, "text_map_screen": "ম্যাপ স্ক্রিন", "@text_map_screen": {"description": "Text from text_widgets: Map Screen", "context": "text_widgets"}, "text_configuration": "কনফিগারেশন", "@text_configuration": {"description": "Text from text_widgets: Configuration", "context": "text_widgets"}, "text_reports": "রিপোর্ট", "@text_reports": {"description": "Text from text_widgets: Reports", "context": "text_widgets"}, "text_passenger_feedback": "যাত্রী ফিডব্যাক", "@text_passenger_feedback": {"description": "Text from text_widgets: Passenger Feedback", "context": "text_widgets"}, "text_rake_deficiency_report_1": "রেক ঘাটতি রিপোর্ট", "@text_rake_deficiency_report_1": {"description": "Text from text_widgets: Rake Deficiency Report", "context": "text_widgets"}, "text_obhs_to_mcc": "OBHS থেকে MCC হস্তান্তর", "@text_obhs_to_mcc": {"description": "Text from text_widgets: OBHS to MCC Handover", "context": "text_widgets"}, "text_mcc_to_obhs": "MCC থেকে OBHS হস্তান্তর", "@text_mcc_to_obhs": {"description": "Text from text_widgets: MCC to OBHS Handover", "context": "text_widgets"}, "text_upload_data": "ডেটা আপলোড করুন", "@text_upload_data": {"description": "Text from text_widgets: Upload data", "context": "text_widgets"}, "text_user_management": "ব্যব<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ব্যবস্থাপনা", "@text_user_management": {"description": "Text from text_widgets: User Management", "context": "text_widgets"}, "text_issue_management": "সমস্যা ব্যবস্থাপনা", "@text_issue_management": {"description": "Text from text_widgets: Issue Management", "context": "text_widgets"}, "text_rail_sathi_qr": "রেল সাথী QR", "@text_rail_sathi_qr": {"description": "Text from text_widgets: Rail Sathi Qr", "context": "text_widgets"}, "text_customer_care": "গ্র<PERSON><PERSON><PERSON> সেবা", "@text_customer_care": {"description": "Text from text_widgets: Customer Care", "context": "text_widgets", "text_loading": "লোড হচ্ছে", "@text_loading": {"description": "Critical translation for: text_loading", "context": "critical_translations"}, "text_error": "ত্রুটি", "@text_error": {"description": "Critical translation for: text_error", "context": "critical_translations"}, "text_success": "সফল", "@text_success": {"description": "Critical translation for: text_success", "context": "critical_translations"}, "text_warning": "সতর্কতা", "@text_warning": {"description": "Critical translation for: text_warning", "context": "critical_translations"}, "text_info": "তথ্য", "@text_info": {"description": "Critical translation for: text_info", "context": "critical_translations"}, "text_no_data": "কোন ডেটা নেই", "@text_no_data": {"description": "Critical translation for: text_no_data", "context": "critical_translations"}, "text_please_wait": "অনুগ্রহ করে অপেক্ষা করুন", "@text_please_wait": {"description": "Critical translation for: text_please_wait", "context": "critical_translations"}, "text_try_again": "আবার চেষ্টা করুন", "@text_try_again": {"description": "Critical translation for: text_try_again", "context": "critical_translations"}, "text_something_went_wrong": "কিছু ভুল হয়েছে", "@text_something_went_wrong": {"description": "Critical translation for: text_something_went_wrong", "context": "critical_translations"}, "text_network_error": "নেটওয়ার্ক ত্রুটি", "@text_network_error": {"description": "Critical translation for: text_network_error", "context": "critical_translations"}, "text_connection_failed": "সংযোগ ব্যর্থ", "@text_connection_failed": {"description": "Critical translation for: text_connection_failed", "context": "critical_translations"}, "text_timeout": "সময় শেষ", "@text_timeout": {"description": "Critical translation for: text_timeout", "context": "critical_translations"}, "text_sign_in": "সাই<PERSON> ইন করুন", "@text_sign_in": {"description": "Critical translation for: text_sign_in", "context": "critical_translations"}, "text_sign_out": "সাইন আউট করুন", "@text_sign_out": {"description": "Critical translation for: text_sign_out", "context": "critical_translations"}, "text_sign_up": "সা<PERSON>ন আপ করুন", "@text_sign_up": {"description": "Critical translation for: text_sign_up", "context": "critical_translations"}, "text_forgot_password": "পাসওয়ার্ড ভুলে গেছেন", "@text_forgot_password": {"description": "Critical translation for: text_forgot_password", "context": "critical_translations"}, "text_reset_password": "পাসওয়ার্ড রিসেট করুন", "@text_reset_password": {"description": "Critical translation for: text_reset_password", "context": "critical_translations"}, "text_change_password": "পাসওয়ার্ড পরিবর্তন করুন", "@text_change_password": {"description": "Critical translation for: text_change_password", "context": "critical_translations"}, "text_current_password": "বর্তমান পাসওয়ার্ড", "@text_current_password": {"description": "Critical translation for: text_current_password", "context": "critical_translations"}, "text_new_password": "নতুন পাসওয়ার্ড", "@text_new_password": {"description": "Critical translation for: text_new_password", "context": "critical_translations"}, "text_confirm_password": "পাসওয়ার্ড নিশ্চিত করুন", "@text_confirm_password": {"description": "Critical translation for: text_confirm_password", "context": "critical_translations"}, "text_invalid_credentials": "অবৈধ শংসাপত্র", "@text_invalid_credentials": {"description": "Critical translation for: text_invalid_credentials", "context": "critical_translations"}, "text_account_locked": "অ্যাকাউন্ট লক", "@text_account_locked": {"description": "Critical translation for: text_account_locked", "context": "critical_translations"}, "text_session_expired": "সেশন শেষ হয়েছে", "@text_session_expired": {"description": "Critical translation for: text_session_expired", "context": "critical_translations"}, "text_train_details": "ট্রেনের বিবরণ", "@text_train_details": {"description": "Critical translation for: text_train_details", "context": "critical_translations"}, "text_train_status": "ট্রেনের অবস্থা", "@text_train_status": {"description": "Critical translation for: text_train_status", "context": "critical_translations"}, "text_train_schedule": "ট্রেনের সময়সূচী", "@text_train_schedule": {"description": "Critical translation for: text_train_schedule", "context": "critical_translations"}, "text_departure_time": "প্রস্থানের সময়", "@text_departure_time": {"description": "Critical translation for: text_departure_time", "context": "critical_translations"}, "text_arrival_time": "আগমনের সময়", "@text_arrival_time": {"description": "Critical translation for: text_arrival_time", "context": "critical_translations"}, "text_platform_number": "প্ল্যাটফর্ম নম্বর", "@text_platform_number": {"description": "Critical translation for: text_platform_number", "context": "critical_translations"}, "text_coach_position": "কোচের অবস্থান", "@text_coach_position": {"description": "Critical translation for: text_coach_position", "context": "critical_translations"}, "text_seat_availability": "আসন উপলব্ধতা", "@text_seat_availability": {"description": "Critical translation for: text_seat_availability", "context": "critical_translations"}, "text_passenger_list": "যাত্রী তালিকা", "@text_passenger_list": {"description": "Critical translation for: text_passenger_list", "context": "critical_translations"}, "text_reservation_chart": "সংরক্ষণ চার্ট", "@text_reservation_chart": {"description": "Critical translation for: text_reservation_chart", "context": "critical_translations"}, "text_waiting_list": "অপেক্ষার তালিকা", "@text_waiting_list": {"description": "Critical translation for: text_waiting_list", "context": "critical_translations"}, "text_confirmed": "নিশ্চিত", "@text_confirmed": {"description": "Critical translation for: text_confirmed", "context": "critical_translations"}, "text_waitlisted": "অপেক্ষার তালিকায়", "@text_waitlisted": {"description": "Critical translation for: text_waitlisted", "context": "critical_translations"}, "text_rac": "RAC", "@text_rac": {"description": "Critical translation for: text_rac", "context": "critical_translations"}, "text_cancelled": "বাতিল", "@text_cancelled": {"description": "Critical translation for: text_cancelled", "context": "critical_translations"}, "text_current_location": "বর্তমান অবস্থান", "@text_current_location": {"description": "Critical translation for: text_current_location", "context": "critical_translations"}, "text_location_permission": "অবস্থান অনুমতি", "@text_location_permission": {"description": "Critical translation for: text_location_permission", "context": "critical_translations"}, "text_enable_location": "অবস্থান সক্ষম করুন", "@text_enable_location": {"description": "Critical translation for: text_enable_location", "context": "critical_translations"}, "text_location_disabled": "অবস্থান নিষ্ক্রিয়", "@text_location_disabled": {"description": "Critical translation for: text_location_disabled", "context": "critical_translations"}, "text_gps_not_available": "GPS উপলব্ধ নয়", "@text_gps_not_available": {"description": "Critical translation for: text_gps_not_available", "context": "critical_translations"}, "text_getting_location": "অবস্থান পাচ্ছি", "@text_getting_location": {"description": "Critical translation for: text_getting_location", "context": "critical_translations"}, "text_location_accuracy": "অবস্থানের নির্ভুলতা", "@text_location_accuracy": {"description": "Critical translation for: text_location_accuracy", "context": "critical_translations"}, "text_distance": "দূরত্ব", "@text_distance": {"description": "Critical translation for: text_distance", "context": "critical_translations"}, "text_latitude": "অক্ষাংশ", "@text_latitude": {"description": "Critical translation for: text_latitude", "context": "critical_translations"}, "text_longitude": "দ্রাঘিমাংশ", "@text_longitude": {"description": "Critical translation for: text_longitude", "context": "critical_translations", "btn_other": "অন্য", "@btn_other": {"description": "Comprehensive translation for: btn_other", "context": "comprehensive_phase1_phase2"}, "btn_other_ca": "অন্য CA", "@btn_other_ca": {"description": "Comprehensive translation for: btn_other_ca", "context": "comprehensive_phase1_phase2"}, "btn_other_ehkobhs": "অন্য EHK/OBHS", "@btn_other_ehkobhs": {"description": "Comprehensive translation for: btn_other_ehkobhs", "context": "comprehensive_phase1_phase2"}, "btn_pending": "অপেক্ষমাণ", "@btn_pending": {"description": "Comprehensive translation for: btn_pending", "context": "comprehensive_phase1_phase2"}, "btn_rake_deficiency_report": "রেক ঘাটতি রিপোর্ট", "@btn_rake_deficiency_report": {"description": "Comprehensive translation for: btn_rake_deficiency_report", "context": "comprehensive_phase1_phase2"}, "btn_save_selection": "নির্বাচন সংরক্ষণ", "@btn_save_selection": {"description": "Comprehensive translation for: btn_save_selection", "context": "comprehensive_phase1_phase2"}, "btn_select_charting_day": "চার্টিং দিন নির্বাচন", "@btn_select_charting_day": {"description": "Comprehensive translation for: btn_select_charting_day", "context": "comprehensive_phase1_phase2"}, "btn_self": "নিজে", "@btn_self": {"description": "Comprehensive translation for: btn_self", "context": "comprehensive_phase1_phase2"}, "btn_upload_json_data": "JSON ডেটা আপলোড", "@btn_upload_json_data": {"description": "Comprehensive translation for: btn_upload_json_data", "context": "comprehensive_phase1_phase2"}, "btn_your_current_location": "আপনার বর্তমান অবস্থান", "@btn_your_current_location": {"description": "Comprehensive translation for: btn_your_current_location", "context": "comprehensive_phase1_phase2"}, "form_add_stoppage": "স্টপেজ যোগ করুন", "@form_add_stoppage": {"description": "Comprehensive translation for: form_add_stoppage", "context": "comprehensive_phase1_phase2"}, "form_add_your_comments": "আপনার মন্তব্য যোগ করুন", "@form_add_your_comments": {"description": "Comprehensive translation for: form_add_your_comments", "context": "comprehensive_phase1_phase2"}, "form_add_your_feedback": "আপনার প্রতিক্রিয়া যোগ করুন", "@form_add_your_feedback": {"description": "Comprehensive translation for: form_add_your_feedback", "context": "comprehensive_phase1_phase2"}, "form_coach_number": "ক<PERSON>চ নম্বর", "@form_coach_number": {"description": "Comprehensive translation for: form_coach_number", "context": "comprehensive_phase1_phase2"}, "form_departure_station": "প্রস্থান স্টেশন", "@form_departure_station": {"description": "Comprehensive translation for: form_departure_station", "context": "comprehensive_phase1_phase2"}, "form_destination_station": "গন্তব্য স্টেশন", "@form_destination_station": {"description": "Comprehensive translation for: form_destination_station", "context": "comprehensive_phase1_phase2"}, "form_enter_coach_number": "কোচ নম্বর প্রবেশ করুন", "@form_enter_coach_number": {"description": "Comprehensive translation for: form_enter_coach_number", "context": "comprehensive_phase1_phase2"}, "form_enter_train_number": "ট্রেন নম্বর প্রবেশ করুন", "@form_enter_train_number": {"description": "Comprehensive translation for: form_enter_train_number", "context": "comprehensive_phase1_phase2"}, "form_journey_date": "যাত্রার তার<PERSON>খ", "@form_journey_date": {"description": "Comprehensive translation for: form_journey_date", "context": "comprehensive_phase1_phase2"}, "form_select_date": "তা<PERSON><PERSON><PERSON> নির<PERSON>বাচন করুন", "@form_select_date": {"description": "Comprehensive translation for: form_select_date", "context": "comprehensive_phase1_phase2"}, "form_select_station": "স্টেশন নির্বাচন করুন", "@form_select_station": {"description": "Comprehensive translation for: form_select_station", "context": "comprehensive_phase1_phase2"}, "form_select_train": "ট্রেন নির্বাচন করুন", "@form_select_train": {"description": "Comprehensive translation for: form_select_train", "context": "comprehensive_phase1_phase2"}, "text_attendance": "উপস্থিতি", "@text_attendance": {"description": "Comprehensive translation for: text_attendance", "context": "comprehensive_phase1_phase2"}, "text_chart_prepared": "চার্ট প্রস্তুত", "@text_chart_prepared": {"description": "Comprehensive translation for: text_chart_prepared", "context": "comprehensive_phase1_phase2"}, "text_coach_assignment": "কোচ নিয়োগ", "@text_coach_assignment": {"description": "Comprehensive translation for: text_coach_assignment", "context": "comprehensive_phase1_phase2"}, "text_current_status": "বর্তমান অবস্থা", "@text_current_status": {"description": "Comprehensive translation for: text_current_status", "context": "comprehensive_phase1_phase2"}, "text_data_sync": "ডেটা সিঙ্ক", "@text_data_sync": {"description": "Comprehensive translation for: text_data_sync", "context": "comprehensive_phase1_phase2"}, "text_download_chart": "চার্ট ডাউনলোড", "@text_download_chart": {"description": "Comprehensive translation for: text_download_chart", "context": "comprehensive_phase1_phase2"}, "text_journey_details": "যাত্রার বিবরণ", "@text_journey_details": {"description": "Comprehensive translation for: text_journey_details", "context": "comprehensive_phase1_phase2"}, "text_location_update": "অবস্থান আপডেট", "@text_location_update": {"description": "Comprehensive translation for: text_location_update", "context": "comprehensive_phase1_phase2"}, "text_passenger_count": "যাত্রী সংখ্যা", "@text_passenger_count": {"description": "Comprehensive translation for: text_passenger_count", "context": "comprehensive_phase1_phase2"}, "text_platform_info": "প্ল্যাটফর্ম তথ্য", "@text_platform_info": {"description": "Comprehensive translation for: text_platform_info", "context": "comprehensive_phase1_phase2"}, "text_rake_composition": "রেক গঠন", "@text_rake_composition": {"description": "Comprehensive translation for: text_rake_composition", "context": "comprehensive_phase1_phase2"}, "text_station_code": "স্টেশন কোড", "@text_station_code": {"description": "Comprehensive translation for: text_station_code", "context": "comprehensive_phase1_phase2"}, "text_train_composition": "ট্রেন গঠন", "@text_train_composition": {"description": "Comprehensive translation for: text_train_composition", "context": "comprehensive_phase1_phase2"}, "text_upload_status": "আপলোড অবস্থা", "@text_upload_status": {"description": "Comprehensive translation for: text_upload_status", "context": "comprehensive_phase1_phase2"}, "error_connection_timeout": "সংযোগ সময় শেষ", "@error_connection_timeout": {"description": "Comprehensive translation for: error_connection_timeout", "context": "comprehensive_phase1_phase2"}, "error_data_not_found": "ডেটা পাওয়া যায়নি", "@error_data_not_found": {"description": "Comprehensive translation for: error_data_not_found", "context": "comprehensive_phase1_phase2"}, "error_invalid_input": "অবৈধ ইনপুট", "@error_invalid_input": {"description": "Comprehensive translation for: error_invalid_input", "context": "comprehensive_phase1_phase2"}, "error_network_unavailable": "নেটওয়ার্ক অনুপলব্ধ", "@error_network_unavailable": {"description": "Comprehensive translation for: error_network_unavailable", "context": "comprehensive_phase1_phase2"}, "error_permission_denied": "অনুমতি প্রত্যাখ্যাত", "@error_permission_denied": {"description": "Comprehensive translation for: error_permission_denied", "context": "comprehensive_phase1_phase2"}, "error_server_error": "সার্ভার ত্রুটি", "@error_server_error": {"description": "Comprehensive translation for: error_server_error", "context": "comprehensive_phase1_phase2"}, "error_upload_failed": "আপলোড ব্যর্থ", "@error_upload_failed": {"description": "Comprehensive translation for: error_upload_failed", "context": "comprehensive_phase1_phase2"}, "success_data_saved": "ডেটা সংরক্ষিত", "@success_data_saved": {"description": "Comprehensive translation for: success_data_saved", "context": "comprehensive_phase1_phase2"}, "success_upload_complete": "আপলোড সম্পূর্ণ", "@success_upload_complete": {"description": "Comprehensive translation for: success_upload_complete", "context": "comprehensive_phase1_phase2"}, "success_sync_complete": "সিঙ্ক সম্পূর্ণ", "@success_sync_complete": {"description": "Comprehensive translation for: success_sync_complete", "context": "comprehensive_phase1_phase2"}, "msg_loading_data": "ডেটা লোড হচ্ছে", "@msg_loading_data": {"description": "Comprehensive translation for: msg_loading_data", "context": "comprehensive_phase1_phase2"}, "msg_processing": "প্রক্রিয়াকরণ", "@msg_processing": {"description": "Comprehensive translation for: msg_processing", "context": "comprehensive_phase1_phase2"}, "msg_please_wait": "অনুগ্রহ করে অপেক্ষা করুন", "@msg_please_wait": {"description": "Comprehensive translation for: msg_please_wait", "context": "comprehensive_phase1_phase2"}, "msg_no_data_available": "কোন ডেটা উপলব্ধ নেই", "@msg_no_data_available": {"description": "Comprehensive translation for: msg_no_data_available", "context": "comprehensive_phase1_phase2"}, "msg_select_option": "বিকল<PERSON><PERSON> নির্বাচন করুন", "@msg_select_option": {"description": "Comprehensive translation for: msg_select_option", "context": "comprehensive_phase1_phase2"}, "nav_dashboard": "ড্যাশবোর্ড", "@nav_dashboard": {"description": "Comprehensive translation for: nav_dashboard", "context": "comprehensive_phase1_phase2"}, "nav_reports": "রিপোর্ট", "@nav_reports": {"description": "Comprehensive translation for: nav_reports", "context": "comprehensive_phase1_phase2"}, "nav_notifications": "বিজ্ঞপ্তি", "@nav_notifications": {"description": "Comprehensive translation for: nav_notifications", "context": "comprehensive_phase1_phase2"}, "nav_profile": "প্রোফাইল", "@nav_profile": {"description": "Comprehensive translation for: nav_profile", "context": "comprehensive_phase1_phase2"}, "nav_help": "সাহায্য", "@nav_help": {"description": "Comprehensive translation for: nav_help", "context": "comprehensive_phase1_phase2"}, "nav_about": "সম্পর্কে", "@nav_about": {"description": "Comprehensive translation for: nav_about", "context": "comprehensive_phase1_phase2"}}}, "text_change_mobile": "মোবাইল পরিবর্তন করুন", "@text_change_mobile": {"description": "Profile screen translation for: text_change_mobile", "context": "profile_screen"}, "text_change_whatsapp": "হোয়াটসঅ্যাপ নম্বর পরিবর্তন করুন", "@text_change_whatsapp": {"description": "Profile screen translation for: text_change_whatsapp", "context": "profile_screen"}, "text_alert": "সতর্কতা", "@text_alert": {"description": "Profile screen translation for: text_alert", "context": "profile_screen"}, "text_close": "বন্ধ করুন", "@text_close": {"description": "Profile screen translation for: text_close", "context": "profile_screen"}, "text_change_your_email": "আপনার ইমেইল পরিবর্তন করুন", "@text_change_your_email": {"description": "Profile screen translation for: text_change_your_email", "context": "profile_screen"}, "text_current_email": "বর্ত<PERSON><PERSON>ন ইমেইল", "@text_current_email": {"description": "Profile screen translation for: text_current_email", "context": "profile_screen"}, "text_new_email": "নতুন ইমেইল", "@text_new_email": {"description": "Profile screen translation for: text_new_email", "context": "profile_screen"}, "text_please_enter_new_email": "অনুগ্রহ করে নতুন ইমেইল প্রবেশ করান", "@text_please_enter_new_email": {"description": "Profile screen translation for: text_please_enter_new_email", "context": "profile_screen"}, "text_otp": "ওটিপি", "@text_otp": {"description": "Profile screen translation for: text_otp", "context": "profile_screen"}, "text_resend_otp": "ওটিপি পুনরায় পাঠান", "@text_resend_otp": {"description": "Profile screen translation for: text_resend_otp", "context": "profile_screen"}, "text_resend_in_seconds": "{seconds} সেকেন্ডে পুনরায় পাঠান", "@text_resend_in_seconds": {"description": "Profile screen translation for: text_resend_in_seconds", "context": "profile_screen"}, "text_verify_otp": "ওটিপি যাচাই করুন", "@text_verify_otp": {"description": "Profile screen translation for: text_verify_otp", "context": "profile_screen"}, "text_generate_otp": "ওটিপি তৈরি করুন", "@text_generate_otp": {"description": "Profile screen translation for: text_generate_otp", "context": "profile_screen"}, "text_change_your_password": "আপনার পাসওয়ার্ড পরিবর্তন করুন", "@text_change_your_password": {"description": "Profile screen translation for: text_change_your_password", "context": "profile_screen"}, "text_old_password": "পুরানো পাসওয়ার্ড", "@text_old_password": {"description": "Profile screen translation for: text_old_password", "context": "profile_screen"}, "text_new_password": "নতুন পাসওয়ার্ড", "@text_new_password": {"description": "Profile screen translation for: text_new_password", "context": "profile_screen"}, "text_confirm_new_password": "নতুন পাসওয়ার্ড নিশ্চিত করুন", "@text_confirm_new_password": {"description": "Profile screen translation for: text_confirm_new_password", "context": "profile_screen"}, "text_please_enter_otp": "অনুগ্রহ করে ওটিপি প্রবেশ করান", "@text_please_enter_otp": {"description": "Profile screen translation for: text_please_enter_otp", "context": "profile_screen"}, "text_send_mobile_otp": "মোবাইল ওটিপি পাঠান", "@text_send_mobile_otp": {"description": "Profile screen translation for: text_send_mobile_otp", "context": "profile_screen"}, "text_send_email_otp": "ইমেইল ওটিপি পাঠান", "@text_send_email_otp": {"description": "Profile screen translation for: text_send_email_otp", "context": "profile_screen"}, "text_please_enter_value": "অনুগ্রহ করে একটি মান প্রবেশ করান", "@text_please_enter_value": {"description": "Profile screen translation for: text_please_enter_value", "context": "profile_screen"}, "text_please_enter_valid_mobile": "অনুগ্রহ করে একটি বৈধ মোবাইল নম্বর প্রবেশ করান", "@text_please_enter_valid_mobile": {"description": "Profile screen translation for: text_please_enter_valid_mobile", "context": "profile_screen"}, "text_success": "সফলতা", "@text_success": {"description": "Profile screen translation for: text_success", "context": "profile_screen"}, "text_ok": "ঠিক আছে", "@text_ok": {"description": "Profile screen translation for: text_ok", "context": "profile_screen"}, "text_change_your_mobile_number": "আপনার মোবাইল নম্বর পরিবর্তন করুন", "@text_change_your_mobile_number": {"description": "Profile screen translation for: text_change_your_mobile_number", "context": "profile_screen"}, "text_current_mobile_number": "বর্ত<PERSON>ান মোবাইল নম্বর", "@text_current_mobile_number": {"description": "Profile screen translation for: text_current_mobile_number", "context": "profile_screen"}, "text_new_mobile_number": "নতুন মোবাইল নম্বর", "@text_new_mobile_number": {"description": "Profile screen translation for: text_new_mobile_number", "context": "profile_screen"}, "text_please_enter_new_mobile": "অনুগ্রহ করে নতুন মোবাইল নম্বর প্রবেশ করান", "@text_please_enter_new_mobile": {"description": "Profile screen translation for: text_please_enter_new_mobile", "context": "profile_screen"}, "text_change_your_whatsapp_number": "আপনার হোয়াটসঅ্যাপ নম্বর পরিবর্তন করুন", "@text_change_your_whatsapp_number": {"description": "Profile screen translation for: text_change_your_whatsapp_number", "context": "profile_screen"}, "text_current_whatsapp_number": "বর্তমান হোয়াটসঅ্যাপ নম্বর", "@text_current_whatsapp_number": {"description": "Profile screen translation for: text_current_whatsapp_number", "context": "profile_screen"}, "text_new_whatsapp_number": "নতুন হোয়াটসঅ্যাপ নম্বর", "@text_new_whatsapp_number": {"description": "Profile screen translation for: text_new_whatsapp_number", "context": "profile_screen"}, "text_please_enter_new_whatsapp": "অনুগ্রহ করে নতুন হোয়াটসঅ্যাপ মোবাইল নম্বর প্রবেশ করান", "@text_please_enter_new_whatsapp": {"description": "Profile screen translation for: text_please_enter_new_whatsapp", "context": "profile_screen"}, "text_train": "ট্রেন", "@text_train": {"description": "Table column header for train", "context": "add_train_screen"}, "text_coaches": "<PERSON><PERSON>চ", "@text_coaches": {"description": "Table column header for coaches", "context": "add_train_screen"}, "text_origin_date": "মূল তারিখ", "@text_origin_date": {"description": "Table column header for origin date", "context": "add_train_screen"}, "text_na": "উপলব্ধ নয়", "@text_na": {"description": "Not available text", "context": "add_train_screen"}, "text_send_otp": "ওটিপি পাঠান", "@text_send_otp": {"description": "Send OTP button text", "context": "change_email_modal"}, "text_failed_to_send_otp": "ওটিপি পাঠাতে ব্যর্থ: {error}", "@text_failed_to_send_otp": {"description": "Error message when OTP sending fails", "context": "change_email_modal", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "text_email_saved_successfully": "ইমেইল সফলভাবে সংরক্ষিত হয়েছে!", "@text_email_saved_successfully": {"description": "Success message when email is saved", "context": "change_email_modal"}, "text_failed_to_verify_otp": "ওটিপি যাচাই করতে ব্যর্থ: {error}", "@text_failed_to_verify_otp": {"description": "Error message when OTP verification fails", "context": "change_email_modal", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "text_cancel": "বাতিল", "@text_cancel": {"description": "Cancel button text", "context": "general"}, "rm_feedback_app_bar_title": "রেল<PERSON><PERSON><PERSON> যাত্রী ফিডব্যাক", "rm_feedback_main_title": "রেলমদদ ফিডব্যাক", "form_pnr_number": "PNR নম্বর *", "form_crn_number": "CRN নম্বর*", "form_train_no": "ট্রেন নম্বর *", "form_train_name": "ট্রেনের নাম *", "form_passenger_name": "যাত্র<PERSON>র নাম *", "form_coach_no": "কোচ নম্বর *", "form_berth_no": "বা<PERSON><PERSON><PERSON> নম্বর *", "form_mobile_number": "মোবাইল নম্বর", "form_email_id": "ইমেইল আইডি", "form_issue_type": "সমস্যার ধরন", "form_sub_issue_type": "উপ সমস্যার ধরন", "form_resolved_status": "সমাধান হয়েছে (হ্যাঁ/না) *", "form_marks": "নম্বর (১ থেকে ১০) *", "form_task_status": "কাজের অবস্থা *", "form_remarks": "যাত্রীর মন্তব্য", "btn_validate": "যাচাই করুন", "btn_verified": "যাচাইকৃত", "btn_verify_email": "ইমেইল যাচাই করুন", "btn_verify_otp": "OTP যাচাই করুন", "btn_submit_feedback": "ফিডব্যাক জমা দিন", "btn_upload_pnr_image": "PNR ছবি আপলোড করুন", "btn_pick_media": "ফিডব্যাকের জন্য ছবি/ভিডিও নির্বাচন করুন", "btn_camera": "ক্যামেরা", "btn_gallery": "গ্যালারি", "btn_image": "ছবি", "btn_video": "ভিডিও", "btn_i_understand": "আমি বুঝেছি", "btn_ok": "ঠিক আছে", "status_verified": "যাচাইকৃত", "status_pending": "অপেক্ষমাণ", "status_completed": "সম্পন্ন", "status_yes": "হ্যাঁ", "status_no": "না", "status_select": "নির্বাচন করুন", "section_email_verification": "ইমেইল যাচাইকরণ (ঐচ্ছিক)", "section_selected_images": "নির্বাচিত ছবি:", "section_selected_videos": "নির্বাচিত ভিডিও:", "dialog_email_verification_info": "ইমেইল যাচাইকরণ তথ্য", "dialog_select_media_type": "মিডিয়া ধরন নির্বাচন করুন", "validation_fill_all_fields": "দয়া করে সমস্ত ক্ষেত্র বৈধ তথ্য দিয়ে পূরণ করুন।", "validation_pnr_digits": "PNR নম্বর ৮ বা ১০ সংখ্যার হতে হবে", "validation_berth_number": "বার্থ নম্বর একটি বৈধ সংখ্যা হতে হবে", "validation_feedback_length": "ফিডব্যাক ১০০ অক্ষরের বেশি হতে পারে না", "validation_email_required": "দয়া করে একটি বৈধ ইমেইল আইডি প্রবেশ করান।", "validation_otp_required": "দয়া করে OTP প্রবেশ করান।", "validation_train_no_required": "ট্রেন নম্বর প্রয়োজন", "validation_train_name_required": "ট্রেনের নাম প্রয়োজন", "validation_passenger_name_required": "যাত্রীর নাম প্রয়োজন", "validation_mobile_required": "মোবাইল নম্বর প্রয়োজন", "validation_mobile_digits": "মোবাইল নম্বর ১০ সংখ্যার হতে হবে", "validation_issue_type_required": "দয়<PERSON> করে একটি সমস্যার ধরন নির্বাচন করুন", "validation_sub_issue_required": "দয়া করে একটি উপ-সমস্যার ধরন নির্বাচন করুন", "validation_resolved_required": "দয়া করে সমাধানের অবস্থা নির্বাচন করুন", "validation_marks_required": "দয<PERSON><PERSON> করে নম্বর নির্বাচন করুন", "msg_pnr_images_limit": "আপনি সর্বোচ্চ ৩টি PNR ছবি নির্বাচন করতে পারেন", "msg_feedback_images_limit": "সর্বোচ্চ ৩টি ফিডব্যাক ছবির অনুমতি আছে", "msg_images_added_limit": "শুধুমাত্র {count}টি ছবি যোগ করা হয়েছে। সর্বোচ্চ ৩টির সীমা পৌঁছেছে।", "msg_error_picking_media": "মিডিয়া নির্বাচনে ত্রুটি: {error}", "msg_failed_fetch_train_name": "ট্রেনের নাম আনতে ব্যর্থ", "msg_invalid_pnr": "অবৈধ PNR নম্বর।", "msg_pnr_success": "PNR বিবরণ সফলভাবে আনা হয়েছে।", "msg_pnr_validation_failed": "PNR বিবরণ যাচাই করতে ব্যর্থ। অবৈধ PNR নম্বর।", "msg_email_verification_sent": "ইমেইল যাচাইকরণ শুরু হয়েছে। দয়া করে আপনার ইনবক্স এবং স্প্যাম ফোল্ডার উভয়ই চেক করুন।।", "msg_otp_verified": "OTP সফলভাবে যাচাই করা হয়েছে।", "msg_feedback_submitted": "ফিডব্যাক সফলভাবে জমা দেওয়া হয়েছে!", "msg_feedback_failed": "ফিডব্যাক জমা দিতে ব্যর্থ", "msg_unexpected_error": "একটি অপ্রত্যাশিত ত্রুটি ঘটেছে। দয়া করে আবার চেষ্টা করুন।", "info_spam_folder_note": "দয়া করে মনে রাখবেন যে যাচাইকরণ ইমেইলগুলি কখনও কখনও আপনার স্প্যাম/জাঙ্ক ফোল্ডারে পৌঁছাতে পারে।", "info_after_requesting_otp": "OTP অনুরোধ করার পর:", "info_check_inbox": "প্র<PERSON><PERSON><PERSON> আপনার ইনবক্স চেক করুন", "info_check_spam": "যদি না পাওয়া যায়, স্প্যাম/জাঙ্ক ফোল্ডার চেক করুন", "info_add_safe_sender": "আমাদের ডোমেইনকে আপনার নিরাপদ প্রেরক তালিকায় যোগ করুন", "text_no_feedback_images": "কোনো ফিডব্যাক ছবি নির্বাচিত নেই", "text_no_pnr_images": "ক<PERSON><PERSON>ো PNR ছবি নির্বাচিত নেই", "text_character_count": "{count}/১০০ অক্ষর", "loading_sending_otp": "OTP পাঠানো হচ্ছে", "loading_verifying_otp": "OTP যাচাই করা হচ্ছে", "loading_submitting_feedback": "ফিডব্যাক জমা দেওয়া হচ্ছে", "attendance_api_summary": "API সারসংক্ষেপ বিস্তারিত", "@attendance_api_summary": {"description": "Attendance: API Summary Details", "context": "attendance"}, "attendance_assigned_coaches": "🚆 বরাদ্দ Coaches:", "@attendance_assigned_coaches": {"description": "Attendance: 🚆 Assigned Coaches:", "context": "attendance"}, "attendance_available_label": "উপলব্ধ: {count}", "@attendance_available_label": {"description": "Attendance: Available: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_available_section": "উপলব্ধ", "@attendance_available_section": {"description": "Attendance: Available", "context": "attendance"}, "attendance_berths_count": "{count} berths", "@attendance_berths_count": {"description": "Attendance: {count} berths", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_buffer_time_restriction": "The সময় between the current সময় and the ট<PERSON>র<PERSON>ন's arrival সময় is not within the 2-hour buffer.", "@attendance_buffer_time_restriction": {"description": "Attendance: The time between the current time and the train's arrival time is not within the 2-hour buffer.", "context": "attendance"}, "attendance_cancel": "বাতিল", "@attendance_cancel": {"description": "Attendance: Cancel", "context": "attendance"}, "attendance_cancelled": "Cancelled", "@attendance_cancelled": {"description": "Attendance: Cancelled", "context": "attendance"}, "attendance_chart_not_prepared": "চার<PERSON><PERSON> has not been prepared for this স্টেশন", "@attendance_chart_not_prepared": {"description": "Attendance: Chart has not been prepared for this station", "context": "attendance"}, "attendance_charting_refreshed": "চার্টিং refreshed at: {time}", "@attendance_charting_refreshed": {"description": "Attendance: Charting refreshed at: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_charting_started": "চার্টিং started at: {time}", "@attendance_charting_started": {"description": "Attendance: Charting started at: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_charting_time": "চার্টিং সময়: {time}", "@attendance_charting_time": {"description": "Attendance: Charting Time: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_click_more": "click for more...", "@attendance_click_more": {"description": "Attendance: click for more...", "context": "attendance"}, "attendance_coach_label": "🚃 কোচ: {coach}", "@attendance_coach_label": {"description": "Attendance: 🚃 Coach: {coach}", "context": "attendance", "placeholders": {"coach": {"type": "String"}}}, "attendance_coach_occupancy": "কোচ Occupancy বিস্তারিত", "@attendance_coach_occupancy": {"description": "Attendance: Coach Occupancy <PERSON>", "context": "attendance"}, "attendance_coach_type": "কোচ Type:", "@attendance_coach_type": {"description": "Attendance: Coach Type:", "context": "attendance"}, "attendance_coaches": "Coaches: {coaches}", "@attendance_coaches": {"description": "Attendance: Coaches: {coaches}", "context": "attendance", "placeholders": {"coaches": {"type": "String"}}}, "attendance_concise_view": "Concise দেখুন", "@attendance_concise_view": {"description": "Attendance: Concise View", "context": "attendance"}, "attendance_count_label": "A: {count}", "@attendance_count_label": {"description": "Attendance: A: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_daily": "Daily", "@attendance_daily": {"description": "Attendance: Daily", "context": "attendance"}, "attendance_data_refreshed": "ডেটা refreshed সফলভাবে", "@attendance_data_refreshed": {"description": "Attendance: Data refreshed successfully", "context": "attendance"}, "attendance_deboarding": "🔴 নামা:", "@attendance_deboarding": {"description": "Attendance: 🔴 Deboarding:", "context": "attendance"}, "attendance_deboarding_none": "🔴 নামা: None", "@attendance_deboarding_none": {"description": "Attendance: 🔴 Deboarding: None", "context": "attendance"}, "attendance_detailed_view": "Detailed দেখুন", "@attendance_detailed_view": {"description": "Attendance: Detailed View", "context": "attendance"}, "attendance_ehk_assigned": "EHK বরাদ্দ for ট্রেন:{ehkName}", "@attendance_ehk_assigned": {"description": "Attendance: EHK Assigned for train:{ehkName}", "context": "attendance", "placeholders": {"ehkName": {"type": "String"}}}, "attendance_expected_charting": "Expected চার্টিং সময়: {time}", "@attendance_expected_charting": {"description": "Attendance: Expected Charting Time: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_failed_load_data": "ব্যর্থ to load detailed ডেটা: {error}", "@attendance_failed_load_data": {"description": "Attendance: Failed to load detailed data: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_failed_update_status": "ব্যর্থ to আপডেট ট্রেন অবস্থা", "@attendance_failed_update_status": {"description": "Attendance: Failed to update train status", "context": "attendance"}, "attendance_go": "যান", "@attendance_go": {"description": "Attendance: Go", "context": "attendance"}, "attendance_in_route": "In-route", "@attendance_in_route": {"description": "Attendance: In-route", "context": "attendance"}, "attendance_inside": "ভিতরে", "@attendance_inside": {"description": "Attendance: Inside", "context": "attendance"}, "attendance_inside_train": "You are now চিহ্নিত as ভিতরে the ট্রেন", "@attendance_inside_train": {"description": "Attendance: You are now marked as inside the train", "context": "attendance"}, "attendance_journey_status_updated": "যাত্রা অবস্থা updated to {status}", "@attendance_journey_status_updated": {"description": "Attendance: Journey status updated to {status}", "context": "attendance", "placeholders": {"status": {"type": "String"}}}, "attendance_last_fetched": "Last fetched: {time}", "@attendance_last_fetched": {"description": "Attendance: Last fetched: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_loading": "লোড হচ্ছে...", "@attendance_loading": {"description": "Attendance: Loading...", "context": "attendance"}, "attendance_location_not_fetched": "ট্রেন অবস্থান is not fetched yet, অনুগ্রহ করে try again later", "@attendance_location_not_fetched": {"description": "Attendance: Train Location is not fetched yet, please try again later", "context": "attendance"}, "attendance_na": "N/A", "@attendance_na": {"description": "Attendance: N/A", "context": "attendance"}, "attendance_near_stations": "You're near the following স্টেশন(s):", "@attendance_near_stations": {"description": "Attendance: You're near the following station(s):", "context": "attendance"}, "attendance_nearby_station_alert": "🛤️ কাছাকাছি স্টেশন সতর্কতা", "@attendance_nearby_station_alert": {"description": "Attendance: 🛤️ Nearby Station Alert", "context": "attendance"}, "attendance_non_sleeper": "Non-Sleeper", "@attendance_non_sleeper": {"description": "Attendance: Non-Sleeper", "context": "attendance"}, "attendance_not_attendance_station": "উপস্থিতি cannot be চিহ্নিত for স্টেশন {stationCode} as it is not an উপস্থিতি স্টেশন.", "@attendance_not_attendance_station": {"description": "Attendance: Attendance cannot be marked for station {stationCode} as it is not an attendance station.", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_not_inside_train": "You are not ভিতরে the ট্রেন. অনুগ্রহ করে যান ভিতরে the ট্রেন first.", "@attendance_not_inside_train": {"description": "Attendance: You are not inside the train. Please go inside the train first.", "context": "attendance"}, "attendance_off_label": "Off: {count}", "@attendance_off_label": {"description": "Attendance: Off: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_offboarding_section": "Offboarding", "@attendance_offboarding_section": {"description": "Attendance: Offboarding", "context": "attendance"}, "attendance_ok": "ঠিক আছে", "@attendance_ok": {"description": "Attendance: OK", "context": "attendance"}, "attendance_on_label": "On: {count}", "@attendance_on_label": {"description": "Attendance: On: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_onboarding": "🟢 উঠা:", "@attendance_onboarding": {"description": "Attendance: 🟢 Onboarding:", "context": "attendance"}, "attendance_onboarding_none": "🟢 উঠা: None", "@attendance_onboarding_none": {"description": "Attendance: 🟢 Onboarding: None", "context": "attendance"}, "attendance_onboarding_section": "উঠা", "@attendance_onboarding_section": {"description": "Attendance: Onboarding", "context": "attendance"}, "attendance_other_ca": "Other CA", "@attendance_other_ca": {"description": "Attendance: Other CA", "context": "attendance"}, "attendance_other_ehk": "Other EHK/OBHS", "@attendance_other_ehk": {"description": "Attendance: Other EHK/OBHS", "context": "attendance"}, "attendance_outside_train": "You are now চিহ্নিত as বাইরে the ট্রেন", "@attendance_outside_train": {"description": "Attendance: You are now marked as outside the train", "context": "attendance"}, "attendance_passenger_chart": "যাত্রী চার্ট   Atten..", "@attendance_passenger_chart": {"description": "Attendance: Passenger Chart   Atten..", "context": "attendance"}, "attendance_refresh_failed": "রিফ্রেশ ব্যর্থ: {error}", "@attendance_refresh_failed": {"description": "Attendance: Refresh failed: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_screen_title": "উপস্থিতি", "@attendance_screen_title": {"description": "Attendance: Attendance", "context": "attendance"}, "attendance_select_date": "নির্বাচন করুন তারিখ", "@attendance_select_date": {"description": "Attendance: Select date", "context": "attendance"}, "attendance_select_train_date": "অনুগ্রহ করে নির্বাচন করুন a ট্রেন নম্বর and তারিখ.", "@attendance_select_train_date": {"description": "Attendance: Please select a train number and date.", "context": "attendance"}, "attendance_select_train_first": "অনুগ্রহ করে নির্বাচন করুন a ট্রেন first", "@attendance_select_train_first": {"description": "Attendance: Please select a train first", "context": "attendance"}, "attendance_self": "Self", "@attendance_self": {"description": "Attendance: Self", "context": "attendance"}, "attendance_sleeper": "Sleeper", "@attendance_sleeper": {"description": "Attendance: Sleeper", "context": "attendance"}, "attendance_station_details": "স্টেশন বিস্তারিত - {stationCode}", "@attendance_station_details": {"description": "Attendance: Station Details - {stationCode}", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_stoppages": "Stoppages", "@attendance_stoppages": {"description": "Attendance: Stoppages", "context": "attendance"}, "attendance_timings": "Tim<PERSON>", "@attendance_timings": {"description": "Attendance: Timings", "context": "attendance"}, "attendance_today": "Today", "@attendance_today": {"description": "Attendance: Today", "context": "attendance"}, "attendance_too_far_from_station": "You're over 50 KM away from the selected স্টেশন {stationCode}. উপস্থিতি can only be চিহ্নিত when you're within the allowed range.", "@attendance_too_far_from_station": {"description": "Attendance: You're over 50 KM away from the selected station {stationCode}. Attendance can only be marked when you're within the allowed range.", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_train": "ট্রেন", "@attendance_train": {"description": "Attendance: Train", "context": "attendance"}, "attendance_train_date": "ট্রেন {trainNo} - {date}", "@attendance_train_date": {"description": "Attendance: Train {trainNo} - {date}", "context": "attendance", "placeholders": {"trainNo": {"type": "String"}, "date": {"type": "String"}}}, "attendance_train_depot": "ট্রেন ডিপো:{depot}", "@attendance_train_depot": {"description": "Attendance: Train Depot:{depot}", "context": "attendance", "placeholders": {"depot": {"type": "String"}}}, "attendance_train_not_running": "ট্রেন Not Running", "@attendance_train_not_running": {"description": "Attendance: Train Not Running", "context": "attendance"}, "attendance_train_not_running_message": "ট্রেন {trainNumber} is NOT running on {dayOfWeek}", "@attendance_train_not_running_message": {"description": "Attendance: Train {trainNumber} is NOT running on {dayOfWeek}", "context": "attendance", "placeholders": {"trainNumber": {"type": "String"}, "dayOfWeek": {"type": "String"}}}, "attendance_update": "আপডেট", "@attendance_update": {"description": "Attendance: Update", "context": "attendance"}, "attendance_update_journey_status": "আপডেট যাত্রা অবস্থা", "@attendance_update_journey_status": {"description": "Attendance: Update Journey Status", "context": "attendance"}, "attendance_update_message": "A new সংস্করণ of the app is উপলব্ধ. You must আপডেট to continue using the app.", "@attendance_update_message": {"description": "Attendance: A new version of the app is available. You must update to continue using the app.", "context": "attendance"}, "attendance_update_now": "আপডেট Now", "@attendance_update_now": {"description": "Attendance: Update Now", "context": "attendance"}, "attendance_update_required": "আপডেট প্রয়োজনীয়", "@attendance_update_required": {"description": "Attendance: Update Required", "context": "attendance"}, "attendance_user_location": "ব্যবহারকারী অবস্থান:{locationStatus}", "@attendance_user_location": {"description": "Attendance: User Location:{locationStatus}", "context": "attendance", "placeholders": {"locationStatus": {"type": "String"}}}, "attendance_details_distance": "দূরত্ব: {distance} km", "@attendance_details_distance": {"description": "Attendance: Distance: {distance} km", "context": "attendance", "placeholders": {"distance": {"type": "String"}}}, "attendance_details_error": "ত্রুটি: {error}", "@attendance_details_error": {"description": "Attendance: Error: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_details_journey_date": "যাত্রা তারিখ:", "@attendance_details_journey_date": {"description": "Attendance: Journey Date:", "context": "attendance"}, "attendance_details_latitude": "latitude: {latitude}", "@attendance_details_latitude": {"description": "Attendance: latitude: {latitude}", "context": "attendance", "placeholders": {"latitude": {"type": "String"}}}, "attendance_details_longitude": "longitude: {longitude}", "@attendance_details_longitude": {"description": "Attendance: longitude: {longitude}", "context": "attendance", "placeholders": {"longitude": {"type": "String"}}}, "attendance_details_match_percentage": "Match শতাংশ: {percentage}", "@attendance_details_match_percentage": {"description": "Attendance: Match Percentage: {percentage}", "context": "attendance", "placeholders": {"percentage": {"type": "String"}}}, "attendance_details_nearest_station": "nearest স্টেশন: {station}", "@attendance_details_nearest_station": {"description": "Attendance: nearest Station: {station}", "context": "attendance", "placeholders": {"station": {"type": "String"}}}, "attendance_details_no_attendance": "No উপস্থিতি found.", "@attendance_details_no_attendance": {"description": "Attendance: No attendance found.", "context": "attendance"}, "attendance_details_no_data": "No ডেটা উপলব্ধ.", "@attendance_details_no_data": {"description": "Attendance: No data available.", "context": "attendance"}, "attendance_details_no_human_detected": "No human detected", "@attendance_details_no_human_detected": {"description": "Attendance: No human detected", "context": "attendance"}, "attendance_details_station_code": "স্টেশন Code:", "@attendance_details_station_code": {"description": "Attendance: Station Code:", "context": "attendance"}, "attendance_details_status": "অবস্থা: {status}", "@attendance_details_status": {"description": "Attendance: Status: {status}", "context": "attendance", "placeholders": {"status": {"type": "String"}}}, "attendance_details_status_marked": "চিহ্নিত", "@attendance_details_status_marked": {"description": "Attendance: Marked", "context": "attendance"}, "attendance_details_status_pending": "Pending", "@attendance_details_status_pending": {"description": "Attendance: Pending", "context": "attendance"}, "attendance_details_title": "উপস্থিতি বিস্তারিত", "@attendance_details_title": {"description": "Attendance: Attendance Details", "context": "attendance"}, "attendance_details_train_number": "ট্রেন নম্বর:", "@attendance_details_train_number": {"description": "Attendance: Train Number:", "context": "attendance"}, "attendance_details_updated": "All বিস্তারিত updated সফলভাবে.", "@attendance_details_updated": {"description": "Attendance: All details updated successfully.", "context": "attendance"}, "attendance_details_updated_at": "Updated At: {updatedAt}", "@attendance_details_updated_at": {"description": "Attendance: Updated At: {updatedAt}", "context": "attendance", "placeholders": {"updatedAt": {"type": "String"}}}, "attendance_details_updated_by": "Updated By: {updatedBy}", "@attendance_details_updated_by": {"description": "Attendance: Updated By: {updatedBy}", "context": "attendance", "placeholders": {"updatedBy": {"type": "String"}}}, "attendance_details_username": "Username: {username}", "@attendance_details_username": {"description": "Attendance: Username: {username}", "context": "attendance", "placeholders": {"username": {"type": "String"}}}, "btn_no_attendance_found": "No উপস্থিতি found.", "@btn_no_attendance_found": {"description": "Attendance: No attendance found.", "context": "attendance"}, "text_attendance_already_submitted": "উপস্থিতি Already Submitted", "@text_attendance_already_submitted": {"description": "Attendance: Attendance Already Submitted", "context": "attendance"}, "text_attendance_marked_successfully": "উপস্থিতি চিহ্নিত সফলভাবে!", "@text_attendance_marked_successfully": {"description": "Attendance: Attendance marked successfully!", "context": "attendance"}, "text_no_attendance_found": "No উপস্থিতি found.", "@text_no_attendance_found": {"description": "Attendance: No attendance found.", "context": "attendance"}, "text_welcome_to_railops": "রেলঅপ্স-এ আপনাকে স্বাগতম", "@text_welcome_to_railops": {"description": "Welcome message on login screen", "context": "login_screen"}, "text_sign_up_to_railops": "রেলঅপ্স-এ সাইন আপ করুন", "@text_sign_up_to_railops": {"description": "Sign up screen title", "context": "sign_up_screen"}, "text_login_using_fingerprint": "ফিঙ্গারপ্রিন্ট ব্যবহার করে লগিন করুন", "@text_login_using_fingerprint": {"description": "Biometric authentication prompt", "context": "authentication"}, "text_logging_in_please_wait": "লগিন হচ্ছে... অনুগ্রহ করে অপেক্ষা করুন।", "@text_logging_in_please_wait": {"description": "Loading message during login", "context": "authentication"}, "text_log_in": "<PERSON><PERSON>", "@text_log_in": {"description": "Login button text", "context": "authentication"}, "text_home": "হোম", "@text_home": {"description": "Home navigation title", "context": "navigation"}, "text_home_screen": "হ<PERSON><PERSON> স্ক্রিন", "@text_home_screen": {"description": "Home screen title text", "context": "home_screen"}, "@form_mobile_number": {"description": "Mobile number field label", "context": "login_form"}, "form_password": "পাসওয়ার্ড *", "@form_password": {"description": "Password field label", "context": "login_form"}, "error_enter_mobile_number": "অনুগ্রহ করে আপনার মোবাইল নম্বর প্রবেশ করান", "@error_enter_mobile_number": {"description": "Mobile number validation error", "context": "login_form"}, "error_mobile_number_digits": "মোবাইল নম্বর ১০ সংখ্যার হতে হবে", "@error_mobile_number_digits": {"description": "Mobile number length validation error", "context": "login_form"}, "error_enter_password": "অনুগ্রহ করে আপনার পাসওয়ার্ড প্রবেশ করান", "@error_enter_password": {"description": "Password validation error", "context": "login_form"}, "btn_log_in_with_mobile": "মোবাইল নম্বর দিয়ে লগ ইন করুন", "@btn_log_in_with_mobile": {"description": "Mobile login button text", "context": "login_form"}, "btn_new_user_sign_up": "নতুন ব্যব<PERSON><PERSON><PERSON>ক<PERSON><PERSON><PERSON>? এখ<PERSON>ন<PERSON> সাইন আপ করুন", "@btn_new_user_sign_up": {"description": "Sign up button text", "context": "login_form"}, "text_privacy_policy": "গোপনীয়তা নীতি", "@text_privacy_policy": {"description": "Privacy policy link text", "context": "login_form"}, "text_terms_conditions": "নিয়<PERSON> ও শর্তাবলী", "@text_terms_conditions": {"description": "Terms and conditions link text", "context": "login_form"}, "text_know_about_app": "এই অ্যাপ সম্পর্কে জানুন", "@text_know_about_app": {"description": "App information link text", "context": "login_form"}, "msg_login_successful": "লগিন সফল", "@msg_login_successful": {"description": "Login success message", "context": "authentication"}, "msg_invalid_pin": "অবৈধ পিন", "@msg_invalid_pin": {"description": "Invalid PIN error message", "context": "authentication"}}