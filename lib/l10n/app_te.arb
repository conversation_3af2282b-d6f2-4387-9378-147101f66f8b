{"@@locale": "te", "appTitle": "రెయిల్ఆప్స్", "@appTitle": {"description": "The title of the application"}, "cancel": "", "confirm": "", "delete": "", "edit": "", "error": "", "home": "", "loading": "", "login": "లోగిన్", "@login": {"description": "Login button text"}, "logout": "", "next": "", "no": "", "ok": "", "password": "", "previous": "", "refresh": "", "retry": "", "save": "", "search": "", "settings": "", "submit": "", "username": "", "welcome": "రెయిల్ఆప్స్కి స్వాగతం", "@welcome": {"description": "Welcome message displayed on home screen"}, "yes": "", "test_welcome": "స్వాగతం", "@test_welcome": {"description": "Welcome message for testing", "context": "test_context"}, "test_login": "లాগిన్", "@test_login": {"description": "Login button text", "context": "button_labels"}, "text_menu": "మెనూ", "@text_menu": {"description": "Text for drawer menu header", "context": "custom_drawer"}, "text_train_tracker": "రైలు ట్రాకర్", "@text_train_tracker": {"description": "Text from text_widgets: Train Tracker", "context": "text_widgets"}, "text_assign_ca": "CA కేటాయించండి", "@text_assign_ca": {"description": "Text from text_widgets: Assign CA", "context": "text_widgets"}, "text_assign_cs": "CS కేటాయించండి", "@text_assign_cs": {"description": "Text from text_widgets: Assign CS", "context": "text_widgets"}, "text_pnr_details": "PNR వివరాలు", "@text_pnr_details": {"description": "Text from text_widgets: PNR Details", "context": "text_widgets"}, "text_passenger_chart": "ప్రయాణికుల చార్ట్", "@text_passenger_chart": {"description": "Text from text_widgets: Passenger Chart", "context": "text_widgets"}, "text_map_screen": "మ్యాప్ స్క్రీన్", "@text_map_screen": {"description": "Text from text_widgets: Map Screen", "context": "text_widgets"}, "text_configuration": "కాన్ఫిగరేషన్", "@text_configuration": {"description": "Text from text_widgets: Configuration", "context": "text_widgets"}, "text_reports": "నివేదికలు", "@text_reports": {"description": "Text from text_widgets: Reports", "context": "text_widgets"}, "text_passenger_feedback": "ప్రయాణికుల అభిప్రాయం", "@text_passenger_feedback": {"description": "Text from text_widgets: Passenger Feedback", "context": "text_widgets"}, "text_rake_deficiency_report_1": "రేక్ లోపం నివేదిక", "@text_rake_deficiency_report_1": {"description": "Text from text_widgets: Rake Deficiency Report", "context": "text_widgets"}, "text_obhs_to_mcc": "OBHS నుండి MCC అప్పగింత", "@text_obhs_to_mcc": {"description": "Text from text_widgets: OBHS to MCC Handover", "context": "text_widgets"}, "text_mcc_to_obhs": "MCC నుండి OBHS అప్పగింత", "@text_mcc_to_obhs": {"description": "Text from text_widgets: MCC to OBHS Handover", "context": "text_widgets"}, "text_upload_data": "డేటా అప్‌లోడ్ చేయండి", "@text_upload_data": {"description": "Text from text_widgets: Upload data", "context": "text_widgets"}, "text_user_management": "వినియోగదారు నిర్వహణ", "@text_user_management": {"description": "Text from text_widgets: User Management", "context": "text_widgets"}, "text_issue_management": "సమస్య నిర్వహణ", "@text_issue_management": {"description": "Text from text_widgets: Issue Management", "context": "text_widgets"}, "text_rail_sathi_qr": "రైల్ సాథి QR", "@text_rail_sathi_qr": {"description": "Text from text_widgets: Rail Sathi Qr", "context": "text_widgets"}, "text_customer_care": "కస్టమర్ కేర్", "@text_customer_care": {"description": "Text from text_widgets: Customer Care", "context": "text_widgets"}, "text_change_mobile": "మొబైల్ మార్చండి", "@text_change_mobile": {"description": "Profile screen translation for: text_change_mobile", "context": "profile_screen"}, "text_change_whatsapp": "వాట్సాప్ నంబర్ మార్చండి", "@text_change_whatsapp": {"description": "Profile screen translation for: text_change_whatsapp", "context": "profile_screen"}, "text_alert": "హెచ్చరిక", "@text_alert": {"description": "Profile screen translation for: text_alert", "context": "profile_screen"}, "text_close": "మూసివేయండి", "@text_close": {"description": "Profile screen translation for: text_close", "context": "profile_screen"}, "text_change_your_email": "మీ ఇమెయిల్ మార్చండి", "@text_change_your_email": {"description": "Profile screen translation for: text_change_your_email", "context": "profile_screen"}, "text_current_email": "ప్రస్తుత ఇమెయిల్", "@text_current_email": {"description": "Profile screen translation for: text_current_email", "context": "profile_screen"}, "text_new_email": "కొత్త ఇమెయిల్", "@text_new_email": {"description": "Profile screen translation for: text_new_email", "context": "profile_screen"}, "text_please_enter_new_email": "దయచేసి కొత్త ఇమెయిల్ నమోదు చేయండి", "@text_please_enter_new_email": {"description": "Profile screen translation for: text_please_enter_new_email", "context": "profile_screen"}, "text_otp": "ఓటిపి", "@text_otp": {"description": "Profile screen translation for: text_otp", "context": "profile_screen"}, "text_resend_otp": "ఓటిపిని మళ్లీ పంపండి", "@text_resend_otp": {"description": "Profile screen translation for: text_resend_otp", "context": "profile_screen"}, "text_resend_in_seconds": "{seconds} సెకన్లలో మళ్లీ పంపండి", "@text_resend_in_seconds": {"description": "Profile screen translation for: text_resend_in_seconds", "context": "profile_screen"}, "text_verify_otp": "ఓటిపిని ధృవీకరించండి", "@text_verify_otp": {"description": "Profile screen translation for: text_verify_otp", "context": "profile_screen"}, "text_generate_otp": "ఓటిపిని రూపొందించండి", "@text_generate_otp": {"description": "Profile screen translation for: text_generate_otp", "context": "profile_screen"}, "text_change_your_password": "మీ పాస్‌వర్డ్ మార్చండి", "@text_change_your_password": {"description": "Profile screen translation for: text_change_your_password", "context": "profile_screen"}, "text_old_password": "పాత పాస్‌వర్డ్", "@text_old_password": {"description": "Profile screen translation for: text_old_password", "context": "profile_screen"}, "text_new_password": "కొత్త పాస్‌వర్డ్", "@text_new_password": {"description": "Profile screen translation for: text_new_password", "context": "profile_screen"}, "text_confirm_new_password": "కొత్త పాస్‌వర్డ్ను నిర్ధారించండి", "@text_confirm_new_password": {"description": "Profile screen translation for: text_confirm_new_password", "context": "profile_screen"}, "text_please_enter_otp": "దయచేసి ఓటిపిని నమోదు చేయండి", "@text_please_enter_otp": {"description": "Profile screen translation for: text_please_enter_otp", "context": "profile_screen"}, "text_send_mobile_otp": "మొబైల్ ఓటిపిని పంపండి", "@text_send_mobile_otp": {"description": "Profile screen translation for: text_send_mobile_otp", "context": "profile_screen"}, "text_send_email_otp": "ఇమెయిల్ ఓటిపిని పంపండి", "@text_send_email_otp": {"description": "Profile screen translation for: text_send_email_otp", "context": "profile_screen"}, "text_please_enter_value": "దయచేసి ఒక విలువను నమోదు చేయండి", "@text_please_enter_value": {"description": "Profile screen translation for: text_please_enter_value", "context": "profile_screen"}, "text_please_enter_valid_mobile": "దయచేసి చెల్లుబాటు అయ్యే మొబైల్ నంబర్ నమోదు చేయండి", "@text_please_enter_valid_mobile": {"description": "Profile screen translation for: text_please_enter_valid_mobile", "context": "profile_screen"}, "text_success": "విజయం", "@text_success": {"description": "Profile screen translation for: text_success", "context": "profile_screen"}, "text_ok": "సరే", "@text_ok": {"description": "Profile screen translation for: text_ok", "context": "profile_screen"}, "text_change_your_mobile_number": "మీ మొబైల్ నంబర్ మార్చండి", "@text_change_your_mobile_number": {"description": "Profile screen translation for: text_change_your_mobile_number", "context": "profile_screen"}, "text_current_mobile_number": "ప్రస్తుత మొబైల్ నంబర్", "@text_current_mobile_number": {"description": "Profile screen translation for: text_current_mobile_number", "context": "profile_screen"}, "text_new_mobile_number": "కొత్త మొబైల్ నంబర్", "@text_new_mobile_number": {"description": "Profile screen translation for: text_new_mobile_number", "context": "profile_screen"}, "text_please_enter_new_mobile": "దయచేసి కొత్త మొబైల్ నంబర్ నమోదు చేయండి", "@text_please_enter_new_mobile": {"description": "Profile screen translation for: text_please_enter_new_mobile", "context": "profile_screen"}, "text_change_your_whatsapp_number": "మీ వాట్సాప్ నంబర్ మార్చండి", "@text_change_your_whatsapp_number": {"description": "Profile screen translation for: text_change_your_whatsapp_number", "context": "profile_screen"}, "text_current_whatsapp_number": "ప్రస్తుత వాట్సాప్ నంబర్", "@text_current_whatsapp_number": {"description": "Profile screen translation for: text_current_whatsapp_number", "context": "profile_screen"}, "text_new_whatsapp_number": "కొత్త వాట్సాప్ నంబర్", "@text_new_whatsapp_number": {"description": "Profile screen translation for: text_new_whatsapp_number", "context": "profile_screen"}, "text_please_enter_new_whatsapp": "దయచేసి కొత్త వాట్సాప్ మొబైల్ నంబర్ నమోదు చేయండి", "@text_please_enter_new_whatsapp": {"description": "Profile screen translation for: text_please_enter_new_whatsapp", "context": "profile_screen"}, "text_train": "రైలు", "@text_train": {"description": "Table column header for train", "context": "add_train_screen"}, "text_coaches": "కోచ్", "@text_coaches": {"description": "Table column header for coaches", "context": "add_train_screen"}, "text_origin_date": "మూల తేదీ", "@text_origin_date": {"description": "Table column header for date", "context": "add_train_screen"}, "text_na": "అందుబాటులో లేదు", "@text_na": {"description": "Table column header for na", "context": "add_train_screen"}, "text_send_otp": "OTP పంపండి", "@text_send_otp": {"description": "Send OTP button text", "context": "change_email_modal"}, "text_failed_to_send_otp": "OTP పంపడంలో విఫలమైంది: {error}", "@text_failed_to_send_otp": {"description": "Error message when OTP sending fails", "context": "change_email_modal", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "text_email_saved_successfully": "ఇమెయిల్ విజయవంతంగా సేవ్ చేయబడింది!", "@text_email_saved_successfully": {"description": "Success message when email is saved", "context": "change_email_modal"}, "text_failed_to_verify_otp": "OTP ధృవీకరణలో విఫలమైంది: {error}", "@text_failed_to_verify_otp": {"description": "Error message when OTP verification fails", "context": "change_email_modal", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "text_cancel": "రద్దు చేయండి", "@text_cancel": {"description": "Cancel button text", "context": "general"}, "rm_feedback_app_bar_title": "రైల్మదద్ ప్రయాణికుల అభిప్రాయం", "rm_feedback_main_title": "రైల్మదద్ అభిప్రాయం", "form_pnr_number": "PNR నంబర్ *", "form_crn_number": "CRN నంబర్*", "form_train_no": "రైలు నంబర్ *", "form_train_name": "రైలు పేరు *", "form_passenger_name": "ప్రయాణికుడి పేరు *", "form_coach_no": "కోచ్ నంబర్ *", "form_berth_no": "బెర్త్ నంబర్ *", "form_mobile_number": "మొబైల్ నంబర్", "form_email_id": "ఇమెయిల్ ఐడి", "form_issue_type": "సమస్య రకం", "form_sub_issue_type": "ఉప సమస్య రకం", "form_resolved_status": "పరిష్కరించబడింది (అవును/లేదు) *", "form_marks": "మార్కులు (1 నుండి 10) *", "form_task_status": "పని స్థితి *", "form_remarks": "ప్రయాణికుడి వ్యాఖ్యలు", "btn_validate": "ధృవీకరించండి", "btn_verified": "ధృవీకరించబడింది", "btn_verify_email": "ఇమెయిల్ ధృవీకరించండి", "btn_verify_otp": "OTP ధృవీకరించండి", "btn_submit_feedback": "అభిప్రాయం సమర్పించండి", "btn_upload_pnr_image": "PNR చిత్రం అప్‌లోడ్ చేయండి", "btn_pick_media": "అభిప్రాయం కోసం చిత్రాలు/వీడియోలు ఎంచుకోండి", "btn_camera": "కెమెరా", "btn_gallery": "గ్యాలరీ", "btn_image": "చిత్రం", "btn_video": "వీడియో", "btn_i_understand": "నేను అర్థం చేసుకున్నాను", "btn_ok": "సరే", "status_verified": "ధృవీకరించబడింది", "status_pending": "పెండింగ్", "status_completed": "పూర్తయింది", "status_yes": "అవును", "status_no": "లేదు", "status_select": "ఎంచుకోండి", "section_email_verification": "ఇమెయిల్ ధృవీకరణ (ఐచ్ఛికం)", "section_selected_images": "ఎంచుకున్న చిత్రాలు:", "section_selected_videos": "ఎంచుకున్న వీడియోలు:", "dialog_email_verification_info": "ఇమెయిల్ ధృవీకరణ సమాచారం", "dialog_select_media_type": "మీడియా రకాన్ని ఎంచుకోండి", "validation_fill_all_fields": "దయచేసి అన్ని ఫీల్డ్‌లను చెల్లుబాటు అయ్యే సమాచారంతో నింపండి.", "validation_pnr_digits": "PNR నంబర్ 8 లేదా 10 అంకెలు ఉండాలి", "validation_berth_number": "బెర్త్ నంబర్ చెల్లుబాటు అయ్యే సంఖ్య ఉండాలి", "validation_feedback_length": "అభిప్రాయం 100 అక్షరాలకు మించకూడదు", "validation_email_required": "దయచేసి చెల్లుబాటు అయ్యే ఇమెయిల్ ఐడిని నమోదు చేయండి.", "validation_otp_required": "దయచేసి OTP నమోదు చేయండి.", "validation_train_no_required": "రైలు నంబర్ అవసరం", "validation_train_name_required": "రైలు పేరు అవసరం", "validation_passenger_name_required": "ప్రయాణికుడి పేరు అవసరం", "validation_mobile_required": "మొబైల్ నంబర్ అవసరం", "validation_mobile_digits": "మొబైల్ నంబర్ 10 అంకెలు ఉండాలి", "validation_issue_type_required": "దయచేసి సమస్య రకాన్ని ఎంచుకోండి", "validation_sub_issue_required": "దయచేసి ఉప-సమస్య రకాన్ని ఎంచుకోండి", "validation_resolved_required": "దయచేసి పరిష్కార స్థితిని ఎంచుకోండి", "validation_marks_required": "దయచేసి మార్కులను ఎంచుకోండి", "msg_pnr_images_limit": "మీరు కేవలం 3 PNR చిత్రాలను మాత్రమే ఎంచుకోవచ్చు", "msg_feedback_images_limit": "గరిష్టంగా 3 అభిప్రాయ చిత్రాలకు అనుమతి ఉంది", "msg_images_added_limit": "కేవలం {count} చిత్రాలు జోడించబడ్డాయి. గరిష్ట 3 పరిమితిని చేరుకుంది.", "msg_error_picking_media": "మీడియా ఎంచుకోవడంలో లోపం: {error}", "msg_failed_fetch_train_name": "రైలు పేరు తీసుకురావడంలో విఫలమైంది", "msg_invalid_pnr": "చెల్లని PNR నంబర్.", "msg_pnr_success": "PNR వివరాలు విజయవంతంగా తీసుకురాబడ్డాయి.", "msg_pnr_validation_failed": "PNR వివరాలను ధృవీకరించడంలో విఫలమైంది. చెల్లని PNR నంబర్.", "msg_email_verification_sent": "ఇమెయిల్ ధృవీకరణ ప్రారంభించబడింది. దయచేసి మీ ఇన్‌బాక్స్ మరియు స్పామ్ ఫోల్డర్ రెండింటినీ తనిఖీ చేయండి..", "msg_otp_verified": "OTP విజయవంతంగా ధృవీకరించబడింది.", "msg_feedback_submitted": "అభిప్రాయం విజయవంతంగా సమర్పించబడింది!", "msg_feedback_failed": "అభిప్రాయం సమర్పించడంలో విఫలమైంది", "msg_unexpected_error": "అనుకోని లోపం సంభవించింది. దయచేసి మళ్లీ ప్రయత్నించండి.", "info_spam_folder_note": "దయచేసి గమనించండి ధృవీకరణ ఇమెయిల్‌లు కొన్నిసార్లు మీ స్పామ్/జంక్ ఫోల్డర్‌కు చేరవచ్చు.", "info_after_requesting_otp": "OTP అభ్యర్థించిన తర్వాత:", "info_check_inbox": "మొదట మీ ఇన్‌బాక్స్‌ను తనిఖీ చేయండి", "info_check_spam": "దొరకకపోతే, స్పామ్/జంక్ ఫోల్డర్‌ను తనిఖీ చేయండి", "info_add_safe_sender": "మా డొమైన్‌ను మీ సురక్షిత పంపేవారి జాబితాకు జోడించండి", "text_no_feedback_images": "అభిప్రాయ చిత్రాలు ఏవీ ఎంచుకోబడలేదు", "text_no_pnr_images": "PNR చిత్రాలు ఏవీ ఎంచుకోబడలేదు", "text_character_count": "{count}/100 అక్షరాలు", "loading_sending_otp": "OTP పంపబడుతోంది", "loading_verifying_otp": "OTP ధృవీకరించబడుతోంది", "loading_submitting_feedback": "అభిప్రాయం సమర్పించబడుతోంది", "attendance_api_summary": "API సారాంశం వివరాలు", "@attendance_api_summary": {"description": "Attendance: API Summary Details", "context": "attendance"}, "attendance_assigned_coaches": "🚆 కేటాయించబడింది Coaches:", "@attendance_assigned_coaches": {"description": "Attendance: 🚆 Assigned Coaches:", "context": "attendance"}, "attendance_available_label": "అందుబాటులో: {count}", "@attendance_available_label": {"description": "Attendance: Available: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_available_section": "అందుబాటులో", "@attendance_available_section": {"description": "Attendance: Available", "context": "attendance"}, "attendance_berths_count": "{count} berths", "@attendance_berths_count": {"description": "Attendance: {count} berths", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_buffer_time_restriction": "The సమయం between the current సమయం and the రైలు's arrival సమయం is not within the 2-hour buffer.", "@attendance_buffer_time_restriction": {"description": "Attendance: The time between the current time and the train's arrival time is not within the 2-hour buffer.", "context": "attendance"}, "attendance_cancel": "రద్దుచేయి", "@attendance_cancel": {"description": "Attendance: Cancel", "context": "attendance"}, "attendance_cancelled": "Cancelled", "@attendance_cancelled": {"description": "Attendance: Cancelled", "context": "attendance"}, "attendance_chart_not_prepared": "చార్ట్ has not been prepared for this స్టేషన్", "@attendance_chart_not_prepared": {"description": "Attendance: Chart has not been prepared for this station", "context": "attendance"}, "attendance_charting_refreshed": "చార్టింగ్ refreshed at: {time}", "@attendance_charting_refreshed": {"description": "Attendance: Charting refreshed at: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_charting_started": "చార్టింగ్ started at: {time}", "@attendance_charting_started": {"description": "Attendance: Charting started at: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_charting_time": "చార్టింగ్ సమయం: {time}", "@attendance_charting_time": {"description": "Attendance: Charting Time: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_click_more": "click for more...", "@attendance_click_more": {"description": "Attendance: click for more...", "context": "attendance"}, "attendance_coach_label": "🚃 కోచ్: {coach}", "@attendance_coach_label": {"description": "Attendance: 🚃 Coach: {coach}", "context": "attendance", "placeholders": {"coach": {"type": "String"}}}, "attendance_coach_occupancy": "కోచ్ Occupancy వివరాలు", "@attendance_coach_occupancy": {"description": "Attendance: Coach Occupancy <PERSON>", "context": "attendance"}, "attendance_coach_type": "కోచ్ Type:", "@attendance_coach_type": {"description": "Attendance: Coach Type:", "context": "attendance"}, "attendance_coaches": "Coaches: {coaches}", "@attendance_coaches": {"description": "Attendance: Coaches: {coaches}", "context": "attendance", "placeholders": {"coaches": {"type": "String"}}}, "attendance_concise_view": "Concise చూడండి", "@attendance_concise_view": {"description": "Attendance: Concise View", "context": "attendance"}, "attendance_count_label": "A: {count}", "@attendance_count_label": {"description": "Attendance: A: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_daily": "Daily", "@attendance_daily": {"description": "Attendance: Daily", "context": "attendance"}, "attendance_data_refreshed": "డేటా refreshed విజయవంతంగా", "@attendance_data_refreshed": {"description": "Attendance: Data refreshed successfully", "context": "attendance"}, "attendance_deboarding": "🔴 దిగడం:", "@attendance_deboarding": {"description": "Attendance: 🔴 Deboarding:", "context": "attendance"}, "attendance_deboarding_none": "🔴 దిగడం: None", "@attendance_deboarding_none": {"description": "Attendance: 🔴 Deboarding: None", "context": "attendance"}, "attendance_detailed_view": "Detailed చూడండి", "@attendance_detailed_view": {"description": "Attendance: Detailed View", "context": "attendance"}, "attendance_ehk_assigned": "EHK కేటాయించబడింది for రైలు:{ehkName}", "@attendance_ehk_assigned": {"description": "Attendance: EHK Assigned for train:{ehkName}", "context": "attendance", "placeholders": {"ehkName": {"type": "String"}}}, "attendance_expected_charting": "Expected చార్టింగ్ సమయం: {time}", "@attendance_expected_charting": {"description": "Attendance: Expected Charting Time: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_failed_load_data": "విఫలమైంది to load detailed డేటా: {error}", "@attendance_failed_load_data": {"description": "Attendance: Failed to load detailed data: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_failed_update_status": "విఫలమైంది to నవీకరించు రైలు స్థితి", "@attendance_failed_update_status": {"description": "Attendance: Failed to update train status", "context": "attendance"}, "attendance_go": "వెళ్లు", "@attendance_go": {"description": "Attendance: Go", "context": "attendance"}, "attendance_in_route": "In-route", "@attendance_in_route": {"description": "Attendance: In-route", "context": "attendance"}, "attendance_inside": "లోపల", "@attendance_inside": {"description": "Attendance: Inside", "context": "attendance"}, "attendance_inside_train": "You are now గుర్తించబడింది as లోపల the రైలు", "@attendance_inside_train": {"description": "Attendance: You are now marked as inside the train", "context": "attendance"}, "attendance_journey_status_updated": "ప్రయాణం స్థితి updated to {status}", "@attendance_journey_status_updated": {"description": "Attendance: Journey status updated to {status}", "context": "attendance", "placeholders": {"status": {"type": "String"}}}, "attendance_last_fetched": "Last fetched: {time}", "@attendance_last_fetched": {"description": "Attendance: Last fetched: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_loading": "లోడ్ అవుతోంది...", "@attendance_loading": {"description": "Attendance: Loading...", "context": "attendance"}, "attendance_location_not_fetched": "రైలు స్థానం is not fetched yet, దయచేసి try again later", "@attendance_location_not_fetched": {"description": "Attendance: Train Location is not fetched yet, please try again later", "context": "attendance"}, "attendance_na": "N/A", "@attendance_na": {"description": "Attendance: N/A", "context": "attendance"}, "attendance_near_stations": "You're near the following స్టేషన్(s):", "@attendance_near_stations": {"description": "Attendance: You're near the following station(s):", "context": "attendance"}, "attendance_nearby_station_alert": "🛤️ సమీపంలోని స్టేషన్ హెచ్చరిక", "@attendance_nearby_station_alert": {"description": "Attendance: 🛤️ Nearby Station Alert", "context": "attendance"}, "attendance_non_sleeper": "Non-Sleeper", "@attendance_non_sleeper": {"description": "Attendance: Non-Sleeper", "context": "attendance"}, "attendance_not_attendance_station": "హాజరు cannot be గుర్తించబడింది for స్టేషన్ {stationCode} as it is not an హాజరు స్టేషన్.", "@attendance_not_attendance_station": {"description": "Attendance: Attendance cannot be marked for station {stationCode} as it is not an attendance station.", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_not_inside_train": "You are not లోపల the రైలు. దయచేసి వెళ్లు లోపల the రైలు first.", "@attendance_not_inside_train": {"description": "Attendance: You are not inside the train. Please go inside the train first.", "context": "attendance"}, "attendance_off_label": "Off: {count}", "@attendance_off_label": {"description": "Attendance: Off: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_offboarding_section": "Offboarding", "@attendance_offboarding_section": {"description": "Attendance: Offboarding", "context": "attendance"}, "attendance_ok": "సరే", "@attendance_ok": {"description": "Attendance: OK", "context": "attendance"}, "attendance_on_label": "On: {count}", "@attendance_on_label": {"description": "Attendance: On: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_onboarding": "🟢 ఎక్కడం:", "@attendance_onboarding": {"description": "Attendance: 🟢 Onboarding:", "context": "attendance"}, "attendance_onboarding_none": "🟢 ఎక్కడం: None", "@attendance_onboarding_none": {"description": "Attendance: 🟢 Onboarding: None", "context": "attendance"}, "attendance_onboarding_section": "ఎక్కడం", "@attendance_onboarding_section": {"description": "Attendance: Onboarding", "context": "attendance"}, "attendance_other_ca": "Other CA", "@attendance_other_ca": {"description": "Attendance: Other CA", "context": "attendance"}, "attendance_other_ehk": "Other EHK/OBHS", "@attendance_other_ehk": {"description": "Attendance: Other EHK/OBHS", "context": "attendance"}, "attendance_outside_train": "You are now గుర్తించబడింది as బయట the రైలు", "@attendance_outside_train": {"description": "Attendance: You are now marked as outside the train", "context": "attendance"}, "attendance_passenger_chart": "ప్రయాణికుడు చార్ట్   Atten..", "@attendance_passenger_chart": {"description": "Attendance: Passenger Chart   Atten..", "context": "attendance"}, "attendance_refresh_failed": "రిఫ్రెష్ విఫలమైంది: {error}", "@attendance_refresh_failed": {"description": "Attendance: Refresh failed: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_screen_title": "హాజరు", "@attendance_screen_title": {"description": "Attendance: Attendance", "context": "attendance"}, "attendance_select_date": "ఎంచుకోండి తేదీ", "@attendance_select_date": {"description": "Attendance: Select date", "context": "attendance"}, "attendance_select_train_date": "దయచేసి ఎంచుకోండి a రైలు సంఖ్య and తేదీ.", "@attendance_select_train_date": {"description": "Attendance: Please select a train number and date.", "context": "attendance"}, "attendance_select_train_first": "దయచేసి ఎంచుకోండి a రైలు first", "@attendance_select_train_first": {"description": "Attendance: Please select a train first", "context": "attendance"}, "attendance_self": "Self", "@attendance_self": {"description": "Attendance: Self", "context": "attendance"}, "attendance_sleeper": "Sleeper", "@attendance_sleeper": {"description": "Attendance: Sleeper", "context": "attendance"}, "attendance_station_details": "స్టేషన్ వివరాలు - {stationCode}", "@attendance_station_details": {"description": "Attendance: Station Details - {stationCode}", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_stoppages": "Stoppages", "@attendance_stoppages": {"description": "Attendance: Stoppages", "context": "attendance"}, "attendance_timings": "Tim<PERSON>", "@attendance_timings": {"description": "Attendance: Timings", "context": "attendance"}, "attendance_today": "Today", "@attendance_today": {"description": "Attendance: Today", "context": "attendance"}, "attendance_too_far_from_station": "You're over 50 KM away from the selected స్టేషన్ {stationCode}. హాజరు can only be గుర్తించబడింది when you're within the allowed range.", "@attendance_too_far_from_station": {"description": "Attendance: You're over 50 KM away from the selected station {stationCode}. Attendance can only be marked when you're within the allowed range.", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_train": "రైలు", "@attendance_train": {"description": "Attendance: Train", "context": "attendance"}, "attendance_train_date": "రైలు {trainNo} - {date}", "@attendance_train_date": {"description": "Attendance: Train {trainNo} - {date}", "context": "attendance", "placeholders": {"trainNo": {"type": "String"}, "date": {"type": "String"}}}, "attendance_train_depot": "రైలు డిపో:{depot}", "@attendance_train_depot": {"description": "Attendance: Train Depot:{depot}", "context": "attendance", "placeholders": {"depot": {"type": "String"}}}, "attendance_train_not_running": "రైలు Not Running", "@attendance_train_not_running": {"description": "Attendance: Train Not Running", "context": "attendance"}, "attendance_train_not_running_message": "రైలు {trainNumber} is NOT running on {dayOfWeek}", "@attendance_train_not_running_message": {"description": "Attendance: Train {trainNumber} is NOT running on {dayOfWeek}", "context": "attendance", "placeholders": {"trainNumber": {"type": "String"}, "dayOfWeek": {"type": "String"}}}, "attendance_update": "నవీకరించు", "@attendance_update": {"description": "Attendance: Update", "context": "attendance"}, "attendance_update_journey_status": "నవీకరించు ప్రయాణం స్థితి", "@attendance_update_journey_status": {"description": "Attendance: Update Journey Status", "context": "attendance"}, "attendance_update_message": "A new వెర్షన్ of the app is అందుబాటులో. You must నవీకరించు to continue using the app.", "@attendance_update_message": {"description": "Attendance: A new version of the app is available. You must update to continue using the app.", "context": "attendance"}, "attendance_update_now": "నవీకరించు Now", "@attendance_update_now": {"description": "Attendance: Update Now", "context": "attendance"}, "attendance_update_required": "నవీకరించు అవసరం", "@attendance_update_required": {"description": "Attendance: Update Required", "context": "attendance"}, "attendance_user_location": "వినియోగదారు స్థానం:{locationStatus}", "@attendance_user_location": {"description": "Attendance: User Location:{locationStatus}", "context": "attendance", "placeholders": {"locationStatus": {"type": "String"}}}, "attendance_details_distance": "దూరం: {distance} km", "@attendance_details_distance": {"description": "Attendance: Distance: {distance} km", "context": "attendance", "placeholders": {"distance": {"type": "String"}}}, "attendance_details_error": "దోషం: {error}", "@attendance_details_error": {"description": "Attendance: Error: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_details_journey_date": "ప్రయాణం తేదీ:", "@attendance_details_journey_date": {"description": "Attendance: Journey Date:", "context": "attendance"}, "attendance_details_latitude": "latitude: {latitude}", "@attendance_details_latitude": {"description": "Attendance: latitude: {latitude}", "context": "attendance", "placeholders": {"latitude": {"type": "String"}}}, "attendance_details_longitude": "longitude: {longitude}", "@attendance_details_longitude": {"description": "Attendance: longitude: {longitude}", "context": "attendance", "placeholders": {"longitude": {"type": "String"}}}, "attendance_details_match_percentage": "Match శాతం: {percentage}", "@attendance_details_match_percentage": {"description": "Attendance: Match Percentage: {percentage}", "context": "attendance", "placeholders": {"percentage": {"type": "String"}}}, "attendance_details_nearest_station": "nearest స్టేషన్: {station}", "@attendance_details_nearest_station": {"description": "Attendance: nearest Station: {station}", "context": "attendance", "placeholders": {"station": {"type": "String"}}}, "attendance_details_no_attendance": "No హాజరు found.", "@attendance_details_no_attendance": {"description": "Attendance: No attendance found.", "context": "attendance"}, "attendance_details_no_data": "No డేటా అందుబాటులో.", "@attendance_details_no_data": {"description": "Attendance: No data available.", "context": "attendance"}, "attendance_details_no_human_detected": "No human detected", "@attendance_details_no_human_detected": {"description": "Attendance: No human detected", "context": "attendance"}, "attendance_details_station_code": "స్టేషన్ Code:", "@attendance_details_station_code": {"description": "Attendance: Station Code:", "context": "attendance"}, "attendance_details_status": "స్థితి: {status}", "@attendance_details_status": {"description": "Attendance: Status: {status}", "context": "attendance", "placeholders": {"status": {"type": "String"}}}, "attendance_details_status_marked": "గుర్తించబడింది", "@attendance_details_status_marked": {"description": "Attendance: Marked", "context": "attendance"}, "attendance_details_status_pending": "Pending", "@attendance_details_status_pending": {"description": "Attendance: Pending", "context": "attendance"}, "attendance_details_title": "హాజరు వివరాలు", "@attendance_details_title": {"description": "Attendance: Attendance Details", "context": "attendance"}, "attendance_details_train_number": "రైలు సంఖ్య:", "@attendance_details_train_number": {"description": "Attendance: Train Number:", "context": "attendance"}, "attendance_details_updated": "All వివరాలు updated విజయవంతంగా.", "@attendance_details_updated": {"description": "Attendance: All details updated successfully.", "context": "attendance"}, "attendance_details_updated_at": "Updated At: {updatedAt}", "@attendance_details_updated_at": {"description": "Attendance: Updated At: {updatedAt}", "context": "attendance", "placeholders": {"updatedAt": {"type": "String"}}}, "attendance_details_updated_by": "Updated By: {updatedBy}", "@attendance_details_updated_by": {"description": "Attendance: Updated By: {updatedBy}", "context": "attendance", "placeholders": {"updatedBy": {"type": "String"}}}, "attendance_details_username": "Username: {username}", "@attendance_details_username": {"description": "Attendance: Username: {username}", "context": "attendance", "placeholders": {"username": {"type": "String"}}}, "btn_no_attendance_found": "No హాజరు found.", "@btn_no_attendance_found": {"description": "Attendance: No attendance found.", "context": "attendance"}, "text_attendance_already_submitted": "హాజరు Already Submitted", "@text_attendance_already_submitted": {"description": "Attendance: Attendance Already Submitted", "context": "attendance"}, "text_attendance_marked_successfully": "హాజరు గుర్తించబడింది విజయవంతంగా!", "@text_attendance_marked_successfully": {"description": "Attendance: Attendance marked successfully!", "context": "attendance"}, "text_no_attendance_found": "No హాజరు found.", "@text_no_attendance_found": {"description": "Attendance: No attendance found.", "context": "attendance"}, "text_welcome_to_railops": "రెయిల్ఆప్స్కి స్వాగతం", "@text_welcome_to_railops": {"description": "Welcome message on login screen", "context": "login_screen"}, "text_sign_up_to_railops": "రెయిల్ఆప్స్లో సైన్ అప్ చేయండి", "@text_sign_up_to_railops": {"description": "Sign up screen title", "context": "sign_up_screen"}, "text_login_using_fingerprint": "వేలిముద్రను ఉపయోగించి లాగిన్ చేయండి", "@text_login_using_fingerprint": {"description": "Biometric authentication prompt", "context": "authentication"}, "text_logging_in_please_wait": "లాగిన్ అవుతోంది... దయచేసి వేచి ఉండండి।", "@text_logging_in_please_wait": {"description": "Loading message during login", "context": "authentication"}, "text_log_in": "లాగ్ ఇన్", "@text_log_in": {"description": "Login button text", "context": "authentication"}, "text_home": "హోమ్", "@text_home": {"description": "Home navigation title", "context": "navigation"}, "text_home_screen": "హోమ్ స్క్రీన్", "@text_home_screen": {"description": "Home screen title text", "context": "home_screen"}, "@form_mobile_number": {"description": "Mobile number field label", "context": "login_form"}, "form_password": "పాస్‌వర్డ్ *", "@form_password": {"description": "Password field label", "context": "login_form"}, "error_enter_mobile_number": "దయచేసి మీ మొబైల్ నంబర్ నమోదు చేయండి", "@error_enter_mobile_number": {"description": "Mobile number validation error", "context": "login_form"}, "error_mobile_number_digits": "మొబైల్ నంబర్ 10 అంకెలు ఉండాలి", "@error_mobile_number_digits": {"description": "Mobile number length validation error", "context": "login_form"}, "error_enter_password": "దయచేసి మీ పాస్‌వర్డ్ నమోదు చేయండి", "@error_enter_password": {"description": "Password validation error", "context": "login_form"}, "btn_log_in_with_mobile": "మొబైల్ నంబర్తో లాగ్ ఇన్ చేయండి", "@btn_log_in_with_mobile": {"description": "Mobile login button text", "context": "login_form"}, "btn_new_user_sign_up": "కొత్త వినియోగదారు? ఇక్కడ సైన్ అప్ చేయండి", "@btn_new_user_sign_up": {"description": "Sign up button text", "context": "login_form"}, "text_privacy_policy": "గోప్యతా విధానం", "@text_privacy_policy": {"description": "Privacy policy link text", "context": "login_form"}, "text_terms_conditions": "నిబంధనలు మరియు షరతులు", "@text_terms_conditions": {"description": "Terms and conditions link text", "context": "login_form"}, "text_know_about_app": "ఈ యాప్ గురించి తెలుసుకోండి", "@text_know_about_app": {"description": "App information link text", "context": "login_form"}, "msg_login_successful": "లాగిన్ విజయవంతం", "@msg_login_successful": {"description": "Login success message", "context": "authentication"}, "msg_invalid_pin": "చెల్లని పిన్", "@msg_invalid_pin": {"description": "Invalid PIN error message", "context": "authentication"}}