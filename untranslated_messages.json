{"as": ["text_reenter_otp", "text_deny", "text_enable", "text_location_access_required", "text_decline", "text_accept", "text_confirm_delete", "text_delete", "text_storage_permission_is", "text_please_select_a", "text_update_required", "text_update_now", "text_please_select_a_1", "text_all_details_updated", "text_failed_to_update", "text_data_refreshed_successfully", "text_train_location_saved", "text_self", "text_other_ca", "text_other_ehkobhs", "text_chart_has_not", "text_camera", "text_gallery", "text_getting_location", "text_error_message", "text_failed_to_get", "text_error_fetching_images", "text_back_to_all", "text_submit", "text_upload_status", "text_compressing_image", "text_upload_entrykeysubstring0_6", "text_image_uploading", "text_error_snapshoterror", "text_latitude_entrylatitude", "text_longitude_entrylongitude", "text_distance_entrydistance_km", "text_updated_by_entryupdatedby", "text_no_data_available", "text_show_more", "text_show_less", "text_could_not_open", "text_download_started", "text_pdf_downloaded_successfully", "text_download", "text_get_in_email", "text_could_not_launch", "text_permission_denied", "text_requested_users", "text_end_date_cannot", "text_please_select_both", "text_update_user_details", "text_request_for_update", "text_i_dont_have", "text_information", "text_request_for_sign", "text_forgot_password", "text_forgotten_password", "text_mobile_otp_login", "text_enter_otp", "text_otp_sent_to_mobile", "text_profile", "text_error", "text_sign_in_with", "text_new_user_sign", "text_please_enter_a", "text_failed_to_fetch", "text_check_pnr_status", "text_pnr_number", "text_no_pnr_data", "text_please_turn_on", "text_an_error_occurred", "text_your_current_location", "text_location_services_disabled", "text_please_enable_location", "text_location_permission_denied", "text_open_settings", "text_location_permission_denied_1", "text_refresh_failed_e", "text_no_location_data", "text_no_data_available_1", "text_no_train_details", "text_none", "text_download_pdf_for", "text_mail_pdf_for", "text_add_configuration", "text_select_charting_day", "text_submitting", "text_return_gap_updated", "text_data_not_refreshed", "text_add", "text_train_details_updated", "text_add_train_details", "text_add_train", "text_stoppages_in_sequence", "text_submitting_train_details", "text_edit_configuration", "text_no_coaches_available", "text_invalid_response_format", "text_please_select_a_2", "text_coach_handover_report", "text_save_selection", "text_select_media_type", "text_image", "text_video", "text_please_select_images", "text_please_select_at", "text_failed_to_upload", "text_error_updating_issue", "text_statustype_by", "text_no_images_available", "text_issuesubissue", "text_status", "text_submitupdate", "text_reportedby", "text_fixedby", "text_resolvedby", "text_pick_images", "text_uploading", "text_submit_upload", "text_retry", "text_approve", "text_no_requests_selected", "text_are_you_sure", "text_requeststoprocesslength_users_approved", "text_confirm_denial", "text_deny_request", "text_approve_selected", "text_approve_all", "text_processing_requests", "text_clear_search", "text_failed_to_fetch_1", "text_error_fetching_trains", "text_traintrainno_traintrainname", "text_other", "text_existing_images", "text_delete_image", "text_image_deleted", "text_newly_selected_images", "text_add_image", "text_complaint_updated", "text_update_failed", "text_save_changes", "text_delete_complaint", "text_are_you_sure_1", "text_complaint_deleted_successfully", "text_failed_to_delete", "text_select_date", "text_no_complaints_found", "text_train_no_complainttrainnumber", "text_date_complaintcomplaindate", "text_pnr_complaintpnrnumber", "text_edit", "text_complaint_submitted_successfully", "text_failed_to_submit", "text_error_e", "text_ehk_ehkdisplay", "text_pending", "text_completed", "text_upload_imagevideo", "text_submit_issue", "text_validate", "text_next", "text_error_loading_authentication", "text_storage_permission_required", "text_please_select_a_3", "text_error_json_file", "text_selection_cleared", "text_upload_json_data", "text_update", "text_add_issue", "text_no_subissues", "text_select_issue", "text_add_subissue", "text_add_new_item", "text_images_or_videos", "text_please_select_an", "text_issues_saved_upload", "text_select_issues_for", "text_pick_imagesvideos", "text_please_select_imagevideo", "text_confirm_deletion", "text_delete_report", "text_coach_issue_status", "text_both_person_and", "text_select_widgetstatustype_by", "text_subissue_widgetname", "text_issue_widgetname", "text_confirm", "text_manage_issues", "text_rake_deficiency_report", "text_upload_pnr_image", "text_pick_imagesvideos_for", "text_please_wait_until", "text_submit_feedback", "text_verify_email", "text_check_your_inbox", "text_if_not_found", "text_add_our_domain", "text_i_understand", "text_nonac", "text_select", "text_failed_to_load", "text_review_feedback", "text_deleting_feedback", "text_feedback_deleted_successfully", "text_error_deleting_feedback", "text_no_feedback_available", "text_train_no_trainnumber", "text_non_ac", "text_message", "text_job_chart_status", "text_please_select_all", "text_update_amount_for", "text_assigned", "text_amount", "text_no_image_url", "text_image_downloaded_successfully", "text_failed_to_download", "text_failed_to_download_1", "text_image_detail", "text_download_image", "text_train_trainnumber_details", "text_confirm_deactivation", "text_proceed", "text_add_email", "text_back", "text_email_verification", "text_phone_verification", "text_logout_confirmation", "text_do_you_want", "text_addupdate", "text_otp_sent_successfully", "text_failed_to_send", "text_please_enter_the", "text_failed_to_verify", "text_uploaded_at_formatteddate", "text_uploaded_by_widgetimageresponsecreatedby", "text_id_widgetimageresponseid", "text_coach_widgetimageresponsecoach", "text_issue_widgetimageresponseissue", "text_delete_confirmation", "text_are_you_sure_2", "text_save", "text_ehkca", "text_image_upload_initiated", "text_failed_to_upload_1", "text_please_select_an_1", "text_jobchart_deleted_successfully", "text_failed_to_delete_1", "text_pick_image", "text_upload_image", "text_failed_to_load_1", "text_language", "text_select_language", "text_rail_sathi", "title_location_access_required", "title_upload_status", "title_compressing_image", "title_upload_entrykeysubstring0_6", "title_permission_denied", "title_confirm_delete", "title_add_new_item", "title_add_issue", "title_add_subissue", "title_select_widgetstatustype_by", "title_update_amount_for", "form_train_number", "form_date", "form_select_date_ddmmmyyyy", "form_first_name", "form_enter_first_name", "form_middle_name_optional", "form_enter_middle_name", "form_last_name", "form_enter_last_name", "form_phone_number", "form_secondary_phone_number", "form_whatsapp_number", "form_enter_10digit_whatsapp", "form_email", "form_enter_your_email", "form_enter_your_first", "form_enter_your_middle", "form_enter_your_last", "form_enter_10digit_secondary", "form_email_1", "form_enter_your_10digit", "form_select_train_numbers", "form_zone", "form_zone_1", "form_employee_id", "form_depot", "form_reenter_password", "form_divisions", "form_divisions_1", "form_select_coaches", "form_middle_name", "form_select_train_number", "form_select_stations", "form_select_date", "form_whatsapp_number_1", "form_enter_secondary_phone", "form_mobile_number_1", "form_enter_mobile_number", "form_enter_mobile_number_1", "form_related_train", "form_division", "form_depot_1", "form_charting_day", "form_from_station", "form_to_station", "form_direction_updown", "form_start_time", "form_eg_0900_am", "form_end_time", "form_eg_0500_pm", "form_charting_time", "form_return_gap_days", "form_inout", "form_related_train_number", "form_updown", "form_train_type", "form_search_train_number", "form_coaches_comma_separated", "form_eg_h_gsl", "form_enter_coach_names", "form_select_days", "form_add_stoppage", "form_type_a_station", "form_search", "form_stoppages_in_sequence", "form_type_a_station_1", "form_frequency", "form_enter_new_coach", "form_use_comma_to", "form_add_your_comments", "form_search_by_name", "form_train", "form_complaint_type", "form_status", "form_write_your_issue", "form_issue_status", "form_name", "form_search_by_train", "form_train_selection", "form_journey_start_date", "form_ddmmyyyy", "form_coach", "form_berth", "form_issue_name", "form_subissue_name", "form_search_1", "form_widgetstatustype_by", "form_select_date_time", "form_add_your_feedback", "form_search_train_number_1", "form_train_number_1", "form_enter_otp", "form_resolved_yesno", "form_train_name_1", "form_marks_1_to", "form_remarks_by_passenger", "form_passenger_name_1", "form_pnr_number_1", "form_crn_number_1", "form_coach_no_1", "form_berth_no_1", "form_task_status_1", "form_feedback", "form_amount_in_hand", "form_select_user", "form_enter_email_otp", "form_enter_phone_otp", "form_select_coaches_optional", "btn_deny", "btn_enable", "btn_decline", "btn_accept", "btn_self", "btn_other_ca", "btn_other_ehkobhs", "btn_close", "btn_error_snapshoterror", "btn_no_data_available", "btn_your_current_location", "btn_no_location_data", "btn_no_data_available_1", "btn_add_configuration", "btn_select_charting_day", "btn_edit_configuration", "btn_no_coaches_available", "btn_coach_handover_report", "btn_statustype_by", "btn_traintrainno_traintrainname", "btn_other", "btn_no_complaints_found", "btn_pending", "btn_completed", "btn_upload_json_data", "btn_cancel", "btn_delete", "btn_save_selection", "btn_coach_issue_status", "btn_rake_deficiency_report", "btn_no_feedback_available", "btn_save", "snackbar_please_select_a", "snackbar_data_refreshed_successfully", "snackbar_train_location_saved", "snackbar_error_fetching_images", "snackbar_download_started", "snackbar_pdf_downloaded_successfully", "snackbar_could_not_launch", "snackbar_refresh_failed_e", "snackbar_return_gap_updated", "snackbar_data_not_refreshed", "snackbar_invalid_response_format", "snackbar_please_select_a_1", "snackbar_please_select_images", "snackbar_please_select_at", "snackbar_failed_to_upload", "snackbar_error_updating_issue", "snackbar_no_images_available", "snackbar_failed_to_fetch", "snackbar_error_fetching_trains", "snackbar_complaint_updated", "snackbar_update_failed", "snackbar_complaint_deleted_successfully", "snackbar_failed_to_delete", "snackbar_failed_to_submit", "snackbar_error_e", "snackbar_issues_saved_upload", "snackbar_feedback_deleted_successfully", "snackbar_error_deleting_feedback", "snackbar_job_chart_status", "snackbar_no_image_url", "snackbar_image_downloaded_successfully", "snackbar_failed_to_download", "snackbar_failed_to_download_1", "snackbar_otp_sent_successfully", "snackbar_failed_to_send", "snackbar_please_enter_the", "snackbar_email_saved_successfully", "snackbar_failed_to_verify", "snackbar_jobchart_deleted_successfully", "snackbar_failed_to_delete_1", "snackbar_failed_to_load", "text_train_details", "text_please_enable_location_services", "text_location_permissions_required", "text_location_permission_denied_forever", "text_location_permissions_permanently_denied", "text_select_all", "text_location_permissions_error", "text_refresh_failed", "text_turn_on_location_services", "text_no_location_data_available", "text_edit_train_details", "text_search_train_number", "text_select_train_number", "text_monday", "text_tuesday", "text_wednesday", "text_thursday", "text_friday", "text_saturday", "text_sunday", "text_updating_data", "text_return_gap_updated_successfully", "text_train_details_updated_successfully", "text_restrictions", "text_location", "text_time", "text_enable_media_upload", "text_failed_to_load_zones", "text_failed_to_load_divisions", "text_failed_to_load_depots", "text_failed_to_load_train_details", "text_error_loading_train_data", "text_edit_profile", "text_error_loading_authentication_state", "text_verify_email_button", "text_first_name", "text_middle_name", "text_last_name", "text_employee_number", "text_role", "text_depot", "text_phone_number", "text_whatsapp_number", "text_secondary_phone_number", "text_email", "text_phone_number_must_be_10_digits", "text_phone_number_and_secondary_phone_number_must_be_different", "text_change_email", "text_change_password", "text_change_mobile_number", "text_change_whatsapp_number", "text_update_profile", "text_add_trains", "text_up", "text_down", "text_today", "text_yesterday", "text_failed_to_load_zones_add", "text_failed_to_load_divisions_add", "text_failed_to_load_depots_add", "text_please_enter_train_number", "text_train_number_min_digits", "text_please_select_return_gap", "text_coaches_sequence", "text_please_enter_train_name", "text_please_select_charting_day", "text_please_enter_from_station", "text_please_enter_to_station", "text_please_select_direction", "text_please_enter_start_time", "text_please_enter_end_time", "text_please_enter_charting_time", "text_please_enter_train_type", "text_add_new_user", "text_phone_and_secondary_phone_must_be_different", "text_validation_error", "text_submitting_data_please_wait", "text_please_complete_all_required_fields", "text_form_incomplete", "text_enter_first_name", "text_please_enter_first_name", "text_middle_name_optional", "text_enter_middle_name", "text_enter_last_name", "text_please_enter_last_name", "text_secondary_phone_number_optional", "text_whatsapp_number_same_as_phone", "text_use_same_number_for_whatsapp", "text_enter_10_digit_whatsapp_number", "text_please_enter_whatsapp_number", "text_whatsapp_number_must_be_10_digits", "text_please_enter_only_numbers", "text_request_for_add_user", "text_information_dialog_title", "text_please_complete_fields_in_order", "text_personal_information", "text_contact_details", "text_account_settings", "text_authentication_check_failed", "text_enter_a_valid_mobile_number", "text_user_data_not_found", "text_access_token_not_found", "text_update_user", "text_enter_mobile_number", "text_search_for_a_user_to_update_their_details", "text_user_not_found_please_check_the_mobile_number_and_try_again", "text_error_with_details", "text_please_enter_your_email", "text_please_enter_valid_email", "text_invalid_email_format", "text_please_enter_valid_mobile_number", "text_please_enter_new_mobile_number", "text_do_you_want_to_logout", "text_yes", "text_no", "text_inside_train", "text_need_alarm", "text_deactivate", "text_logout", "text_email_otp_error", "text_phone_otp_error", "text_please_select_a_train_number", "text_add_update", "text_an_error_occurred_while_adding", "text_select_coaches_optional", "text_select_coaches_from_list", "text_please_select_date", "text_need_valid_email_before_deactivation", "text_send_otps_for_verification", "text_failed_to_send_otps", "text_enter_email_otp", "text_enter_phone_otp", "text_please_enter_field", "text_enable_fingerprint_login", "text_deactivate_account", "text_email_verification_optional", "text_verified", "text_email_verification_info", "text_otp_sent_message", "error_berth_number_invalid", "error_form_validation_failed", "error_max_videos", "error_max_videos_short", "error_max_feedback_images", "error_max_feedback_images_short", "error_max_images_reached", "error_picking_media", "error_email_required", "msg_email_verification_initiated", "error_otp_required", "error_something_wrong", "text_daily", "text_yes_lowercase", "text_no_lowercase", "error_berth_number_invalid_passenger", "error_no_train_selected", "error_fetching_charting_time", "dialog_train_not_running_content", "error_validating_pnr", "error_feedback_submission_passenger", "error_max_images_reached_passenger", "error_picking_media_passenger", "error_generic_passenger", "error_something_wrong_passenger", "text_check_inbox", "text_check_spam", "text_add_safe_sender", "msg_otp_sent_check_folders", "msg_error_generic", "msg_something_went_wrong", "status_yes_lowercase", "status_no_lowercase", "passenger_feedback_app_bar_title", "passenger_feedback_main_title", "form_train_name_readonly", "hint_search_train_number", "category_ac", "category_nonac", "category_tt", "category_rm", "rating_excellent", "rating_very_good", "rating_good", "rating_average", "rating_poor", "feedback_table_instruction", "feedback_table_item_header", "dialog_train_not_running", "msg_train_not_running_details", "msg_running_days_daily", "msg_running_days_na", "validation_provide_rating", "msg_rm_fields_required", "msg_upload_complete_wait", "msg_videos_limit", "msg_videos_limit_reached", "feedback_ac_item_1", "feedback_ac_item_2", "feedback_ac_item_3", "feedback_ac_item_4", "feedback_ac_item_5", "feedback_nonac_item_1", "feedback_nonac_item_2", "feedback_nonac_item_3", "button_ok", "error_fetch_train_name", "error_invalid_pnr", "success_pnr_fetched", "error_pnr_validation", "success_feedback_submitted", "error_feedback_submission", "error_unexpected", "email_verification_info_title", "email_verification_spam_notice", "email_verification_after_otp", "email_verification_understand", "success_email_verification", "error_prefix", "success_otp_verified", "upload_pnr_image", "selected_images", "no_pnr_images_selected", "upload_limit_pnr_3", "media_source_camera", "media_source_gallery", "upload_limit_pnr_max", "upload_limit_reached_message", "error_picking_media_simple", "pick_images_videos_feedback", "no_feedback_images_selected", "selected_videos", "select_media_type", "media_type_image", "media_type_video", "upload_limit_videos_3", "upload_limit_videos_max", "upload_limit_feedback_images_3", "upload_limit_feedback_images_max", "text_pnr_document", "text_feedback_media", "loading_deleting_feedback", "success_feedback_deleted", "error_deleting_feedback", "text_train_no", "text_not_verified", "btn_review", "btn_retry", "msg_no_feedback_available", "dialog_title_review_feedback", "text_status_pending_editable", "text_status_completed_not_editable", "text_pnr_documents", "text_failed_to_load_image", "text_tap_to_play_video", "form_remarks_hint", "form_resolved_yes_no", "form_marks_1_to_10", "status_none_selected", "btn_update", "validation_select_issue_type", "validation_select_sub_issue_type", "validation_select_resolved_status", "validation_select_marks", "validation_enter_valid_email", "validation_enter_otp", "validation_remarks_max_100", "msg_cannot_verify_email_completed", "msg_cannot_verify_otp_completed", "msg_otp_verified_successfully", "msg_feedback_updated_successfully", "msg_failed_to_update_feedback", "msg_failed_to_load_video", "msg_error_with_details", "msg_error_or_something_wrong", "loading_updating_feedback", "text_character_count_100", "dialog_email_verification_info_title", "dialog_email_verification_spam_notice", "dialog_after_requesting_otp", "dialog_check_inbox_first", "dialog_check_spam_folder", "dialog_add_safe_sender", "text_otp_sent_check_folders", "form_issue_type_label", "form_sub_issue_type_label", "form_task_status_label", "status_pending_option", "status_completed_option", "text_need_help_reach_out", "error_could_not_launch_phone", "text_notifications", "tooltip_test_notifications", "tooltip_notification_settings", "tooltip_mark_all_as_read", "tooltip_clear_all_notifications", "text_no_notifications", "dialog_title_clear_notifications", "dialog_content_clear_notifications", "btn_clear", "text_notification_fallback", "text_coach_details", "table_header_station", "table_header_coach", "table_header_onboard", "table_header_deboard", "table_header_vacant", "text_notification_system_tests", "text_notification_system_testing", "text_notification_test_description", "text_ready_to_run_notification_tests", "btn_run_all_tests", "btn_running_tests", "btn_individual_tests", "text_test_results", "text_no_test_results_yet", "text_run_tests_to_see_results", "text_running_comprehensive_tests", "text_all_tests_completed_successfully", "text_some_tests_failed", "text_test_execution_failed", "dialog_title_select_test", "test_fcm_token_generation", "test_firestore_token_storage", "test_complete_notification_sync", "test_cloud_function_endpoint", "test_enhanced_notification", "test_debug_firestore_token_storage", "test_force_token_refresh", "test_quick_token_check", "text_running_test", "text_test_completed_successfully", "text_test_failed", "text_no_test_data_available", "text_test_passed", "text_test_failed_label", "rail_sathi_app_bar_title", "tab_write_complaint", "tab_view_complaints", "error_could_not_launch_link", "error_failed_to_load_issues", "error_invalid_response_format", "error_failed_to_load_trip_report", "msg_data_refreshed_successfully", "error_refresh_failed", "error_select_train_date_first", "text_no_coaches_found", "text_please_select_train_date", "text_coach_label", "text_no_issues_reported", "text_reported_issues", "text_unknown_issue", "btn_manage_issues", "text_train_rake_deficiency_report", "text_rake_deficiency_report_issues", "tooltip_upload_coach_image", "btn_upload_image_video", "btn_submit_issue", "form_pnr", "form_search_by_train_number_or_name", "form_dd_mm_yyyy", "btn_next", "tab_cleaning_issues", "tab_linen_related_issues", "msg_please_fill_all_required_details", "text_ehk_not_assigned", "msg_pnr_validation_error_e", "msg_are_you_sure_delete_image", "text_selected_date", "text_date", "text_pnr", "msg_are_you_sure_delete_complaint", "reports_app_bar_title", "btn_attendance_report", "btn_round_rake_deficiency_report", "btn_train_report", "btn_detailed_attendance_report", "btn_detailed_round_trip_attendance_report", "btn_obhs_to_mcc_handover", "btn_mcc_to_obhs_handover", "btn_monthly_attendance_report", "btn_monthly_round_trip_attendance_report", "text_daily_reports", "text_monthly_reports", "btn_show_all", "btn_show_less", "section_daily_reports", "section_monthly_reports", "section_attendance_for", "report_attendance", "report_detailed_attendance", "report_detailed_round_trip", "report_obhs_to_mcc_handover", "report_monthly_attendance", "report_monthly_round_trip", "report_monthly_with_mobile", "text_notification_settings", "text_save_settings", "text_onboarding_notifications", "text_enable_onboarding_notifications", "text_station_approach_alerts", "text_boarding_alerts", "text_off_boarding_alerts", "text_proximity_alerts", "text_timing_settings", "text_advance_notice_minutes", "text_proximity_threshold_km", "text_coach_filters", "text_enable_coach_specific_filtering", "text_enabled_coach_types", "text_sound_vibration", "text_enable_sound", "text_enable_vibration", "text_advanced_settings", "text_background_notifications", "text_location_based_notifications", "text_max_notifications_per_hour", "msg_notification_preferences_saved", "msg_error_saving_preferences", "error_fetching_trains", "form_mobile", "form_enter_train_number", "form_description", "dropdown_other", "dropdown_cleaning", "dropdown_linen", "dropdown_pending", "dropdown_completed", "dialog_delete_image", "dialog_delete_image_confirm", "dialog_delete_complaint", "dialog_delete_complaint_confirm", "btn_add_image", "btn_save_changes", "btn_edit", "btn_select_date", "text_edit_complaint", "form_train_label", "error_failed_to_fetch_complaints", "text_berth_no", "msg_image_deleted", "msg_complaint_updated", "msg_update_failed", "msg_complaint_deleted_success", "msg_failed_to_delete_complaint", "snackbar_image_deleted", "snackbar_failed_to_delete_image", "snackbar_failed_to_delete_complaint", "snackbar_failed_to_fetch_complaints", "btn_rake_deficiency_report_simple", "btn_monthly_with_mobile", "btn_monthly_without_mobile", "text_info", "text_reports_for", "text_trains_not_running", "text_train_not_running", "text_please_select_relevant_trains", "text_following_trains_not_running", "text_train_label", "text_running_days_label", "text_running_trains_on", "text_train_not_running_message", "pnr_status_title", "hint_enter_pnr_number", "label_pnr_number", "btn_check_pnr", "text_no_pnr_data_found", "error_invalid_pnr_number", "error_failed_fetch_pnr", "label_train_number", "label_train_name", "label_boarding_date", "label_from", "label_to", "label_class", "label_departure", "label_arrival", "label_overall_status", "label_booking_date", "label_passenger", "label_coach_berth", "label_ehk_users", "label_ca_users", "label_obhs_users"], "bn": ["text_reenter_otp", "text_deny", "text_enable", "text_location_access_required", "text_decline", "text_accept", "text_confirm_delete", "text_delete", "text_storage_permission_is", "text_please_select_a", "text_update_required", "text_update_now", "text_please_select_a_1", "text_all_details_updated", "text_failed_to_update", "text_data_refreshed_successfully", "text_train_location_saved", "text_self", "text_other_ca", "text_other_ehkobhs", "text_chart_has_not", "text_camera", "text_gallery", "text_getting_location", "text_error_message", "text_failed_to_get", "text_error_fetching_images", "text_back_to_all", "text_submit", "text_upload_status", "text_compressing_image", "text_upload_entrykeysubstring0_6", "text_image_uploading", "text_error_snapshoterror", "text_latitude_entrylatitude", "text_longitude_entrylongitude", "text_distance_entrydistance_km", "text_updated_by_entryupdatedby", "text_no_data_available", "text_show_more", "text_show_less", "text_could_not_open", "text_download_started", "text_pdf_downloaded_successfully", "text_download", "text_get_in_email", "text_could_not_launch", "text_permission_denied", "text_requested_users", "text_end_date_cannot", "text_please_select_both", "text_update_user_details", "text_request_for_update", "text_i_dont_have", "text_information", "text_request_for_sign", "text_forgot_password", "text_forgotten_password", "text_mobile_otp_login", "text_enter_otp", "text_otp_sent_to_mobile", "text_profile", "text_error", "text_sign_in_with", "text_new_user_sign", "text_please_enter_a", "text_failed_to_fetch", "text_check_pnr_status", "text_pnr_number", "text_no_pnr_data", "text_please_turn_on", "text_an_error_occurred", "text_your_current_location", "text_location_services_disabled", "text_please_enable_location", "text_location_permission_denied", "text_open_settings", "text_location_permission_denied_1", "text_refresh_failed_e", "text_no_location_data", "text_no_data_available_1", "text_no_train_details", "text_none", "text_download_pdf_for", "text_mail_pdf_for", "text_add_configuration", "text_select_charting_day", "text_submitting", "text_return_gap_updated", "text_data_not_refreshed", "text_add", "text_train_details_updated", "text_add_train_details", "text_add_train", "text_stoppages_in_sequence", "text_submitting_train_details", "text_edit_configuration", "text_no_coaches_available", "text_invalid_response_format", "text_please_select_a_2", "text_coach_handover_report", "text_save_selection", "text_select_media_type", "text_image", "text_video", "text_please_select_images", "text_please_select_at", "text_failed_to_upload", "text_error_updating_issue", "text_statustype_by", "text_no_images_available", "text_issuesubissue", "text_status", "text_submitupdate", "text_reportedby", "text_fixedby", "text_resolvedby", "text_pick_images", "text_uploading", "text_submit_upload", "text_retry", "text_approve", "text_no_requests_selected", "text_are_you_sure", "text_requeststoprocesslength_users_approved", "text_confirm_denial", "text_deny_request", "text_approve_selected", "text_approve_all", "text_processing_requests", "text_clear_search", "text_failed_to_fetch_1", "text_error_fetching_trains", "text_traintrainno_traintrainname", "text_other", "text_existing_images", "text_delete_image", "text_image_deleted", "text_newly_selected_images", "text_add_image", "text_complaint_updated", "text_update_failed", "text_save_changes", "text_delete_complaint", "text_are_you_sure_1", "text_complaint_deleted_successfully", "text_failed_to_delete", "text_select_date", "text_no_complaints_found", "text_train_no_complainttrainnumber", "text_date_complaintcomplaindate", "text_pnr_complaintpnrnumber", "text_edit", "text_complaint_submitted_successfully", "text_failed_to_submit", "text_error_e", "text_ehk_ehkdisplay", "text_pending", "text_completed", "text_upload_imagevideo", "text_submit_issue", "text_validate", "text_next", "text_error_loading_authentication", "text_storage_permission_required", "text_please_select_a_3", "text_error_json_file", "text_selection_cleared", "text_upload_json_data", "text_update", "text_add_issue", "text_no_subissues", "text_select_issue", "text_add_subissue", "text_add_new_item", "text_images_or_videos", "text_please_select_an", "text_issues_saved_upload", "text_select_issues_for", "text_pick_imagesvideos", "text_please_select_imagevideo", "text_confirm_deletion", "text_delete_report", "text_coach_issue_status", "text_both_person_and", "text_select_widgetstatustype_by", "text_subissue_widgetname", "text_issue_widgetname", "text_confirm", "text_manage_issues", "text_rake_deficiency_report", "text_upload_pnr_image", "text_pick_imagesvideos_for", "text_please_wait_until", "text_submit_feedback", "text_verify_email", "text_check_your_inbox", "text_if_not_found", "text_add_our_domain", "text_i_understand", "text_nonac", "text_select", "text_failed_to_load", "text_review_feedback", "text_deleting_feedback", "text_feedback_deleted_successfully", "text_error_deleting_feedback", "text_no_feedback_available", "text_train_no_trainnumber", "text_non_ac", "text_message", "text_job_chart_status", "text_please_select_all", "text_update_amount_for", "text_assigned", "text_amount", "text_no_image_url", "text_image_downloaded_successfully", "text_failed_to_download", "text_failed_to_download_1", "text_image_detail", "text_download_image", "text_train_trainnumber_details", "text_confirm_deactivation", "text_proceed", "text_add_email", "text_back", "text_email_verification", "text_phone_verification", "text_logout_confirmation", "text_do_you_want", "text_addupdate", "text_otp_sent_successfully", "text_failed_to_send", "text_please_enter_the", "text_failed_to_verify", "text_uploaded_at_formatteddate", "text_uploaded_by_widgetimageresponsecreatedby", "text_id_widgetimageresponseid", "text_coach_widgetimageresponsecoach", "text_issue_widgetimageresponseissue", "text_delete_confirmation", "text_are_you_sure_2", "text_save", "text_ehkca", "text_image_upload_initiated", "text_failed_to_upload_1", "text_please_select_an_1", "text_jobchart_deleted_successfully", "text_failed_to_delete_1", "text_pick_image", "text_upload_image", "text_failed_to_load_1", "text_language", "text_select_language", "text_rail_sathi", "title_location_access_required", "title_upload_status", "title_compressing_image", "title_upload_entrykeysubstring0_6", "title_permission_denied", "title_confirm_delete", "title_add_new_item", "title_add_issue", "title_add_subissue", "title_select_widgetstatustype_by", "title_update_amount_for", "form_train_number", "form_date", "form_select_date_ddmmmyyyy", "form_first_name", "form_enter_first_name", "form_middle_name_optional", "form_enter_middle_name", "form_last_name", "form_enter_last_name", "form_phone_number", "form_secondary_phone_number", "form_whatsapp_number", "form_enter_10digit_whatsapp", "form_email", "form_enter_your_email", "form_enter_your_first", "form_enter_your_middle", "form_enter_your_last", "form_enter_10digit_secondary", "form_email_1", "form_enter_your_10digit", "form_select_train_numbers", "form_zone", "form_zone_1", "form_employee_id", "form_depot", "form_reenter_password", "form_divisions", "form_divisions_1", "form_select_coaches", "form_middle_name", "form_select_train_number", "form_select_stations", "form_select_date", "form_whatsapp_number_1", "form_enter_secondary_phone", "form_mobile_number_1", "form_enter_mobile_number", "form_enter_mobile_number_1", "form_related_train", "form_division", "form_depot_1", "form_charting_day", "form_from_station", "form_to_station", "form_direction_updown", "form_start_time", "form_eg_0900_am", "form_end_time", "form_eg_0500_pm", "form_charting_time", "form_return_gap_days", "form_inout", "form_related_train_number", "form_updown", "form_train_type", "form_search_train_number", "form_coaches_comma_separated", "form_eg_h_gsl", "form_enter_coach_names", "form_select_days", "form_add_stoppage", "form_type_a_station", "form_search", "form_stoppages_in_sequence", "form_type_a_station_1", "form_frequency", "form_enter_new_coach", "form_use_comma_to", "form_add_your_comments", "form_search_by_name", "form_train", "form_complaint_type", "form_status", "form_write_your_issue", "form_issue_status", "form_name", "form_search_by_train", "form_train_selection", "form_journey_start_date", "form_ddmmyyyy", "form_coach", "form_berth", "form_issue_name", "form_subissue_name", "form_search_1", "form_widgetstatustype_by", "form_select_date_time", "form_add_your_feedback", "form_search_train_number_1", "form_train_number_1", "form_enter_otp", "form_resolved_yesno", "form_train_name_1", "form_marks_1_to", "form_remarks_by_passenger", "form_passenger_name_1", "form_pnr_number_1", "form_crn_number_1", "form_coach_no_1", "form_berth_no_1", "form_task_status_1", "form_feedback", "form_amount_in_hand", "form_select_user", "form_enter_email_otp", "form_enter_phone_otp", "form_select_coaches_optional", "btn_deny", "btn_enable", "btn_decline", "btn_accept", "btn_self", "btn_other_ca", "btn_other_ehkobhs", "btn_close", "btn_error_snapshoterror", "btn_no_data_available", "btn_your_current_location", "btn_no_location_data", "btn_no_data_available_1", "btn_add_configuration", "btn_select_charting_day", "btn_edit_configuration", "btn_no_coaches_available", "btn_coach_handover_report", "btn_statustype_by", "btn_traintrainno_traintrainname", "btn_other", "btn_no_complaints_found", "btn_pending", "btn_completed", "btn_upload_json_data", "btn_cancel", "btn_delete", "btn_save_selection", "btn_coach_issue_status", "btn_rake_deficiency_report", "btn_no_feedback_available", "btn_save", "snackbar_please_select_a", "snackbar_data_refreshed_successfully", "snackbar_train_location_saved", "snackbar_error_fetching_images", "snackbar_download_started", "snackbar_pdf_downloaded_successfully", "snackbar_could_not_launch", "snackbar_refresh_failed_e", "snackbar_return_gap_updated", "snackbar_data_not_refreshed", "snackbar_invalid_response_format", "snackbar_please_select_a_1", "snackbar_please_select_images", "snackbar_please_select_at", "snackbar_failed_to_upload", "snackbar_error_updating_issue", "snackbar_no_images_available", "snackbar_failed_to_fetch", "snackbar_error_fetching_trains", "snackbar_complaint_updated", "snackbar_update_failed", "snackbar_complaint_deleted_successfully", "snackbar_failed_to_delete", "snackbar_failed_to_submit", "snackbar_error_e", "snackbar_issues_saved_upload", "snackbar_feedback_deleted_successfully", "snackbar_error_deleting_feedback", "snackbar_job_chart_status", "snackbar_no_image_url", "snackbar_image_downloaded_successfully", "snackbar_failed_to_download", "snackbar_failed_to_download_1", "snackbar_otp_sent_successfully", "snackbar_failed_to_send", "snackbar_please_enter_the", "snackbar_email_saved_successfully", "snackbar_failed_to_verify", "snackbar_jobchart_deleted_successfully", "snackbar_failed_to_delete_1", "snackbar_failed_to_load", "text_train_details", "text_please_enable_location_services", "text_location_permissions_required", "text_location_permission_denied_forever", "text_location_permissions_permanently_denied", "text_select_all", "text_location_permissions_error", "text_refresh_failed", "text_turn_on_location_services", "text_no_location_data_available", "text_edit_train_details", "text_search_train_number", "text_select_train_number", "text_monday", "text_tuesday", "text_wednesday", "text_thursday", "text_friday", "text_saturday", "text_sunday", "text_updating_data", "text_return_gap_updated_successfully", "text_train_details_updated_successfully", "text_restrictions", "text_location", "text_time", "text_enable_media_upload", "text_failed_to_load_zones", "text_failed_to_load_divisions", "text_failed_to_load_depots", "text_failed_to_load_train_details", "text_error_loading_train_data", "text_edit_profile", "text_error_loading_authentication_state", "text_verify_email_button", "text_first_name", "text_middle_name", "text_last_name", "text_employee_number", "text_role", "text_depot", "text_phone_number", "text_whatsapp_number", "text_secondary_phone_number", "text_email", "text_phone_number_must_be_10_digits", "text_phone_number_and_secondary_phone_number_must_be_different", "text_change_email", "text_change_password", "text_change_mobile_number", "text_change_whatsapp_number", "text_update_profile", "text_add_trains", "text_up", "text_down", "text_today", "text_yesterday", "text_failed_to_load_zones_add", "text_failed_to_load_divisions_add", "text_failed_to_load_depots_add", "text_please_enter_train_number", "text_train_number_min_digits", "text_please_select_return_gap", "text_coaches_sequence", "text_please_enter_train_name", "text_please_select_charting_day", "text_please_enter_from_station", "text_please_enter_to_station", "text_please_select_direction", "text_please_enter_start_time", "text_please_enter_end_time", "text_please_enter_charting_time", "text_please_enter_train_type", "text_add_new_user", "text_phone_and_secondary_phone_must_be_different", "text_validation_error", "text_submitting_data_please_wait", "text_please_complete_all_required_fields", "text_form_incomplete", "text_enter_first_name", "text_please_enter_first_name", "text_middle_name_optional", "text_enter_middle_name", "text_enter_last_name", "text_please_enter_last_name", "text_secondary_phone_number_optional", "text_whatsapp_number_same_as_phone", "text_use_same_number_for_whatsapp", "text_enter_10_digit_whatsapp_number", "text_please_enter_whatsapp_number", "text_whatsapp_number_must_be_10_digits", "text_please_enter_only_numbers", "text_request_for_add_user", "text_information_dialog_title", "text_please_complete_fields_in_order", "text_personal_information", "text_contact_details", "text_account_settings", "text_authentication_check_failed", "text_enter_a_valid_mobile_number", "text_user_data_not_found", "text_access_token_not_found", "text_update_user", "text_enter_mobile_number", "text_search_for_a_user_to_update_their_details", "text_user_not_found_please_check_the_mobile_number_and_try_again", "text_error_with_details", "text_please_enter_your_email", "text_please_enter_valid_email", "text_invalid_email_format", "text_please_enter_valid_mobile_number", "text_please_enter_new_mobile_number", "text_do_you_want_to_logout", "text_yes", "text_no", "text_inside_train", "text_need_alarm", "text_deactivate", "text_logout", "text_email_otp_error", "text_phone_otp_error", "text_please_select_a_train_number", "text_add_update", "text_an_error_occurred_while_adding", "text_select_coaches_optional", "text_select_coaches_from_list", "text_please_select_date", "text_need_valid_email_before_deactivation", "text_send_otps_for_verification", "text_failed_to_send_otps", "text_enter_email_otp", "text_enter_phone_otp", "text_please_enter_field", "text_enable_fingerprint_login", "text_deactivate_account", "text_email_verification_optional", "text_verified", "text_email_verification_info", "text_otp_sent_message", "error_berth_number_invalid", "error_form_validation_failed", "error_max_videos", "error_max_videos_short", "error_max_feedback_images", "error_max_feedback_images_short", "error_max_images_reached", "error_picking_media", "error_email_required", "msg_email_verification_initiated", "error_otp_required", "error_something_wrong", "text_daily", "text_yes_lowercase", "text_no_lowercase", "error_berth_number_invalid_passenger", "error_no_train_selected", "error_fetching_charting_time", "dialog_train_not_running_content", "error_validating_pnr", "error_feedback_submission_passenger", "error_max_images_reached_passenger", "error_picking_media_passenger", "error_generic_passenger", "error_something_wrong_passenger", "text_check_inbox", "text_check_spam", "text_add_safe_sender", "msg_otp_sent_check_folders", "msg_error_generic", "msg_something_went_wrong", "status_yes_lowercase", "status_no_lowercase", "passenger_feedback_app_bar_title", "passenger_feedback_main_title", "form_train_name_readonly", "hint_search_train_number", "category_ac", "category_nonac", "category_tt", "category_rm", "rating_excellent", "rating_very_good", "rating_good", "rating_average", "rating_poor", "feedback_table_instruction", "feedback_table_item_header", "dialog_train_not_running", "msg_train_not_running_details", "msg_running_days_daily", "msg_running_days_na", "validation_provide_rating", "msg_rm_fields_required", "msg_upload_complete_wait", "msg_videos_limit", "msg_videos_limit_reached", "feedback_ac_item_1", "feedback_ac_item_2", "feedback_ac_item_3", "feedback_ac_item_4", "feedback_ac_item_5", "feedback_nonac_item_1", "feedback_nonac_item_2", "feedback_nonac_item_3", "button_ok", "error_fetch_train_name", "error_invalid_pnr", "success_pnr_fetched", "error_pnr_validation", "success_feedback_submitted", "error_feedback_submission", "error_unexpected", "email_verification_info_title", "email_verification_spam_notice", "email_verification_after_otp", "email_verification_understand", "success_email_verification", "error_prefix", "success_otp_verified", "upload_pnr_image", "selected_images", "no_pnr_images_selected", "upload_limit_pnr_3", "media_source_camera", "media_source_gallery", "upload_limit_pnr_max", "upload_limit_reached_message", "error_picking_media_simple", "pick_images_videos_feedback", "no_feedback_images_selected", "selected_videos", "select_media_type", "media_type_image", "media_type_video", "upload_limit_videos_3", "upload_limit_videos_max", "upload_limit_feedback_images_3", "upload_limit_feedback_images_max", "text_pnr_document", "text_feedback_media", "loading_deleting_feedback", "success_feedback_deleted", "error_deleting_feedback", "text_train_no", "text_not_verified", "btn_review", "btn_retry", "msg_no_feedback_available", "dialog_title_review_feedback", "text_status_pending_editable", "text_status_completed_not_editable", "text_pnr_documents", "text_failed_to_load_image", "text_tap_to_play_video", "form_remarks_hint", "form_resolved_yes_no", "form_marks_1_to_10", "status_none_selected", "btn_update", "validation_select_issue_type", "validation_select_sub_issue_type", "validation_select_resolved_status", "validation_select_marks", "validation_enter_valid_email", "validation_enter_otp", "validation_remarks_max_100", "msg_cannot_verify_email_completed", "msg_cannot_verify_otp_completed", "msg_otp_verified_successfully", "msg_feedback_updated_successfully", "msg_failed_to_update_feedback", "msg_failed_to_load_video", "msg_error_with_details", "msg_error_or_something_wrong", "loading_updating_feedback", "text_character_count_100", "dialog_email_verification_info_title", "dialog_email_verification_spam_notice", "dialog_after_requesting_otp", "dialog_check_inbox_first", "dialog_check_spam_folder", "dialog_add_safe_sender", "text_otp_sent_check_folders", "form_issue_type_label", "form_sub_issue_type_label", "form_task_status_label", "status_pending_option", "status_completed_option", "text_need_help_reach_out", "error_could_not_launch_phone", "text_notifications", "tooltip_test_notifications", "tooltip_notification_settings", "tooltip_mark_all_as_read", "tooltip_clear_all_notifications", "text_no_notifications", "dialog_title_clear_notifications", "dialog_content_clear_notifications", "btn_clear", "text_notification_fallback", "text_coach_details", "table_header_station", "table_header_coach", "table_header_onboard", "table_header_deboard", "table_header_vacant", "text_notification_system_tests", "text_notification_system_testing", "text_notification_test_description", "text_ready_to_run_notification_tests", "btn_run_all_tests", "btn_running_tests", "btn_individual_tests", "text_test_results", "text_no_test_results_yet", "text_run_tests_to_see_results", "text_running_comprehensive_tests", "text_all_tests_completed_successfully", "text_some_tests_failed", "text_test_execution_failed", "dialog_title_select_test", "test_fcm_token_generation", "test_firestore_token_storage", "test_complete_notification_sync", "test_cloud_function_endpoint", "test_enhanced_notification", "test_debug_firestore_token_storage", "test_force_token_refresh", "test_quick_token_check", "text_running_test", "text_test_completed_successfully", "text_test_failed", "text_no_test_data_available", "text_test_passed", "text_test_failed_label", "rail_sathi_app_bar_title", "tab_write_complaint", "tab_view_complaints", "error_could_not_launch_link", "error_failed_to_load_issues", "error_invalid_response_format", "error_failed_to_load_trip_report", "msg_data_refreshed_successfully", "error_refresh_failed", "error_select_train_date_first", "text_no_coaches_found", "text_please_select_train_date", "text_coach_label", "text_no_issues_reported", "text_reported_issues", "text_unknown_issue", "btn_manage_issues", "text_train_rake_deficiency_report", "text_rake_deficiency_report_issues", "tooltip_upload_coach_image", "btn_upload_image_video", "btn_submit_issue", "form_pnr", "form_search_by_train_number_or_name", "form_dd_mm_yyyy", "btn_next", "tab_cleaning_issues", "tab_linen_related_issues", "msg_please_fill_all_required_details", "text_ehk_not_assigned", "msg_pnr_validation_error_e", "msg_are_you_sure_delete_image", "text_selected_date", "text_date", "text_pnr", "msg_are_you_sure_delete_complaint", "reports_app_bar_title", "btn_attendance_report", "btn_round_rake_deficiency_report", "btn_train_report", "btn_detailed_attendance_report", "btn_detailed_round_trip_attendance_report", "btn_obhs_to_mcc_handover", "btn_mcc_to_obhs_handover", "btn_monthly_attendance_report", "btn_monthly_round_trip_attendance_report", "text_daily_reports", "text_monthly_reports", "btn_show_all", "btn_show_less", "section_daily_reports", "section_monthly_reports", "section_attendance_for", "report_attendance", "report_detailed_attendance", "report_detailed_round_trip", "report_obhs_to_mcc_handover", "report_monthly_attendance", "report_monthly_round_trip", "report_monthly_with_mobile", "text_notification_settings", "text_save_settings", "text_onboarding_notifications", "text_enable_onboarding_notifications", "text_station_approach_alerts", "text_boarding_alerts", "text_off_boarding_alerts", "text_proximity_alerts", "text_timing_settings", "text_advance_notice_minutes", "text_proximity_threshold_km", "text_coach_filters", "text_enable_coach_specific_filtering", "text_enabled_coach_types", "text_sound_vibration", "text_enable_sound", "text_enable_vibration", "text_advanced_settings", "text_background_notifications", "text_location_based_notifications", "text_max_notifications_per_hour", "msg_notification_preferences_saved", "msg_error_saving_preferences", "error_fetching_trains", "form_mobile", "form_enter_train_number", "form_description", "dropdown_other", "dropdown_cleaning", "dropdown_linen", "dropdown_pending", "dropdown_completed", "dialog_delete_image", "dialog_delete_image_confirm", "dialog_delete_complaint", "dialog_delete_complaint_confirm", "btn_add_image", "btn_save_changes", "btn_edit", "btn_select_date", "text_edit_complaint", "form_train_label", "error_failed_to_fetch_complaints", "text_berth_no", "msg_image_deleted", "msg_complaint_updated", "msg_update_failed", "msg_complaint_deleted_success", "msg_failed_to_delete_complaint", "snackbar_image_deleted", "snackbar_failed_to_delete_image", "snackbar_failed_to_delete_complaint", "snackbar_failed_to_fetch_complaints", "btn_rake_deficiency_report_simple", "btn_monthly_with_mobile", "btn_monthly_without_mobile", "text_info", "text_reports_for", "text_trains_not_running", "text_train_not_running", "text_please_select_relevant_trains", "text_following_trains_not_running", "text_train_label", "text_running_days_label", "text_running_trains_on", "text_train_not_running_message", "pnr_status_title", "hint_enter_pnr_number", "label_pnr_number", "btn_check_pnr", "text_no_pnr_data_found", "error_invalid_pnr_number", "error_failed_fetch_pnr", "label_train_number", "label_train_name", "label_boarding_date", "label_from", "label_to", "label_class", "label_departure", "label_arrival", "label_overall_status", "label_booking_date", "label_passenger", "label_coach_berth", "label_ehk_users", "label_ca_users", "label_obhs_users"], "gu": ["text_reenter_otp", "text_deny", "text_enable", "text_location_access_required", "text_decline", "text_accept", "text_confirm_delete", "text_cancel", "text_delete", "text_storage_permission_is", "text_please_select_a", "text_update_required", "text_update_now", "text_please_select_a_1", "text_all_details_updated", "text_failed_to_update", "text_data_refreshed_successfully", "text_train_location_saved", "text_self", "text_other_ca", "text_other_ehkobhs", "text_chart_has_not", "text_camera", "text_gallery", "text_getting_location", "text_error_message", "text_failed_to_get", "text_error_fetching_images", "text_back_to_all", "text_submit", "text_upload_status", "text_compressing_image", "text_upload_entrykeysubstring0_6", "text_close", "text_image_uploading", "text_error_snapshoterror", "text_latitude_entrylatitude", "text_longitude_entrylongitude", "text_distance_entrydistance_km", "text_updated_by_entryupdatedby", "text_no_data_available", "text_show_more", "text_show_less", "text_could_not_open", "text_download_started", "text_pdf_downloaded_successfully", "text_download", "text_get_in_email", "text_could_not_launch", "text_permission_denied", "text_requested_users", "text_end_date_cannot", "text_please_select_both", "text_update_user_details", "text_request_for_update", "text_i_dont_have", "text_information", "text_request_for_sign", "text_forgot_password", "text_forgotten_password", "text_mobile_otp_login", "text_enter_otp", "text_otp_sent_to_mobile", "text_profile", "text_error", "text_send_otp", "text_verify_otp", "text_sign_in_with", "text_new_user_sign", "text_resend_otp", "text_please_enter_a", "text_failed_to_fetch", "text_check_pnr_status", "text_pnr_number", "text_no_pnr_data", "text_please_turn_on", "text_an_error_occurred", "text_your_current_location", "text_location_services_disabled", "text_please_enable_location", "text_location_permission_denied", "text_open_settings", "text_location_permission_denied_1", "text_refresh_failed_e", "text_no_location_data", "text_no_data_available_1", "text_no_train_details", "text_none", "text_download_pdf_for", "text_mail_pdf_for", "text_add_configuration", "text_select_charting_day", "text_submitting", "text_return_gap_updated", "text_data_not_refreshed", "text_add", "text_train_details_updated", "text_add_train_details", "text_add_train", "text_stoppages_in_sequence", "text_submitting_train_details", "text_edit_configuration", "text_no_coaches_available", "text_invalid_response_format", "text_please_select_a_2", "text_coach_handover_report", "text_save_selection", "text_select_media_type", "text_image", "text_video", "text_please_select_images", "text_please_select_at", "text_failed_to_upload", "text_error_updating_issue", "text_statustype_by", "text_no_images_available", "text_issuesubissue", "text_status", "text_submitupdate", "text_reportedby", "text_fixedby", "text_resolvedby", "text_pick_images", "text_uploading", "text_submit_upload", "text_retry", "text_approve", "text_no_requests_selected", "text_are_you_sure", "text_requeststoprocesslength_users_approved", "text_confirm_denial", "text_deny_request", "text_approve_selected", "text_approve_all", "text_processing_requests", "text_clear_search", "text_failed_to_fetch_1", "text_error_fetching_trains", "text_traintrainno_traintrainname", "text_other", "text_existing_images", "text_delete_image", "text_image_deleted", "text_newly_selected_images", "text_add_image", "text_complaint_updated", "text_update_failed", "text_save_changes", "text_delete_complaint", "text_are_you_sure_1", "text_complaint_deleted_successfully", "text_failed_to_delete", "text_select_date", "text_no_complaints_found", "text_train_no_complainttrainnumber", "text_date_complaintcomplaindate", "text_pnr_complaintpnrnumber", "text_edit", "text_success", "text_complaint_submitted_successfully", "text_failed_to_submit", "text_error_e", "text_ehk_ehkdisplay", "text_pending", "text_completed", "text_upload_imagevideo", "text_submit_issue", "text_validate", "text_next", "text_error_loading_authentication", "text_storage_permission_required", "text_please_select_a_3", "text_error_json_file", "text_selection_cleared", "text_upload_json_data", "text_update", "text_add_issue", "text_no_subissues", "text_select_issue", "text_add_subissue", "text_add_new_item", "text_images_or_videos", "text_please_select_an", "text_issues_saved_upload", "text_select_issues_for", "text_pick_imagesvideos", "text_please_select_imagevideo", "text_confirm_deletion", "text_delete_report", "text_coach_issue_status", "text_both_person_and", "text_select_widgetstatustype_by", "text_subissue_widgetname", "text_issue_widgetname", "text_confirm", "text_manage_issues", "text_rake_deficiency_report", "text_upload_pnr_image", "text_pick_imagesvideos_for", "text_please_wait_until", "text_submit_feedback", "text_verify_email", "text_check_your_inbox", "text_if_not_found", "text_add_our_domain", "text_i_understand", "text_nonac", "text_select", "text_failed_to_load", "text_review_feedback", "text_deleting_feedback", "text_feedback_deleted_successfully", "text_error_deleting_feedback", "text_no_feedback_available", "text_train_no_trainnumber", "text_non_ac", "text_message", "text_job_chart_status", "text_please_select_all", "text_update_amount_for", "text_assigned", "text_amount", "text_no_image_url", "text_image_downloaded_successfully", "text_failed_to_download", "text_failed_to_download_1", "text_image_detail", "text_download_image", "text_train_trainnumber_details", "text_confirm_deactivation", "text_proceed", "text_add_email", "text_back", "text_email_verification", "text_phone_verification", "text_logout_confirmation", "text_do_you_want", "text_addupdate", "text_alert", "text_generate_otp", "text_otp_sent_successfully", "text_failed_to_send", "text_please_enter_the", "text_email_saved_successfully", "text_failed_to_verify", "text_send_mobile_otp", "text_send_email_otp", "text_uploaded_at_formatteddate", "text_uploaded_by_widgetimageresponsecreatedby", "text_id_widgetimageresponseid", "text_coach_widgetimageresponsecoach", "text_issue_widgetimageresponseissue", "text_delete_confirmation", "text_are_you_sure_2", "text_save", "text_ehkca", "text_image_upload_initiated", "text_failed_to_upload_1", "text_please_select_an_1", "text_jobchart_deleted_successfully", "text_failed_to_delete_1", "text_pick_image", "text_upload_image", "text_failed_to_load_1", "text_language", "text_select_language", "text_train_tracker", "text_assign_ca", "text_assign_cs", "text_pnr_details", "text_rail_sathi", "text_passenger_chart", "text_map_screen", "text_configuration", "text_reports", "text_passenger_feedback", "text_rake_deficiency_report_1", "text_obhs_to_mcc", "text_mcc_to_obhs", "text_upload_data", "text_user_management", "text_issue_management", "text_rail_sathi_qr", "text_customer_care", "text_menu", "title_location_access_required", "title_upload_status", "title_compressing_image", "title_upload_entrykeysubstring0_6", "title_permission_denied", "title_confirm_delete", "title_add_new_item", "title_add_issue", "title_add_subissue", "title_select_widgetstatustype_by", "title_update_amount_for", "form_train_number", "form_date", "form_select_date_ddmmmyyyy", "form_first_name", "form_enter_first_name", "form_middle_name_optional", "form_enter_middle_name", "form_last_name", "form_enter_last_name", "form_phone_number", "form_secondary_phone_number", "form_whatsapp_number", "form_enter_10digit_whatsapp", "form_email", "form_enter_your_email", "form_enter_your_first", "form_enter_your_middle", "form_enter_your_last", "form_enter_10digit_secondary", "form_email_1", "form_mobile_number", "form_enter_your_10digit", "form_password", "form_select_train_numbers", "form_zone", "form_zone_1", "form_employee_id", "form_depot", "form_reenter_password", "form_divisions", "form_divisions_1", "form_select_coaches", "form_middle_name", "form_select_train_number", "form_train_name", "form_select_stations", "form_select_date", "form_whatsapp_number_1", "form_enter_secondary_phone", "form_mobile_number_1", "form_enter_mobile_number", "form_enter_mobile_number_1", "form_related_train", "form_division", "form_depot_1", "form_charting_day", "form_from_station", "form_to_station", "form_direction_updown", "form_start_time", "form_eg_0900_am", "form_end_time", "form_eg_0500_pm", "form_charting_time", "form_return_gap_days", "form_inout", "form_related_train_number", "form_updown", "form_train_type", "form_search_train_number", "form_coaches_comma_separated", "form_eg_h_gsl", "form_enter_coach_names", "form_select_days", "form_add_stoppage", "form_type_a_station", "form_search", "form_stoppages_in_sequence", "form_type_a_station_1", "form_frequency", "form_enter_new_coach", "form_use_comma_to", "form_add_your_comments", "form_search_by_name", "form_train", "form_complaint_type", "form_status", "form_write_your_issue", "form_issue_status", "form_name", "form_search_by_train", "form_train_selection", "form_journey_start_date", "form_ddmmyyyy", "form_coach", "form_berth", "form_issue_name", "form_subissue_name", "form_search_1", "form_widgetstatustype_by", "form_select_date_time", "form_add_your_feedback", "form_task_status", "form_search_train_number_1", "form_train_number_1", "form_pnr_number", "form_passenger_name", "form_coach_no", "form_berth_no", "form_email_id", "form_enter_otp", "form_issue_type", "form_sub_issue_type", "form_resolved_yesno", "form_crn_number", "form_train_no", "form_train_name_1", "form_marks_1_to", "form_remarks_by_passenger", "form_passenger_name_1", "form_pnr_number_1", "form_crn_number_1", "form_coach_no_1", "form_berth_no_1", "form_remarks", "form_task_status_1", "form_feedback", "form_amount_in_hand", "form_select_user", "form_enter_email_otp", "form_enter_phone_otp", "form_select_coaches_optional", "btn_deny", "btn_enable", "btn_decline", "btn_accept", "btn_self", "btn_other_ca", "btn_other_ehkobhs", "btn_close", "btn_error_snapshoterror", "btn_no_data_available", "btn_your_current_location", "btn_no_location_data", "btn_no_data_available_1", "btn_add_configuration", "btn_select_charting_day", "btn_edit_configuration", "btn_no_coaches_available", "btn_coach_handover_report", "btn_statustype_by", "btn_traintrainno_traintrainname", "btn_other", "btn_no_complaints_found", "btn_pending", "btn_completed", "btn_upload_json_data", "btn_cancel", "btn_delete", "btn_save_selection", "btn_coach_issue_status", "btn_rake_deficiency_report", "btn_no_feedback_available", "btn_save", "snackbar_please_select_a", "snackbar_data_refreshed_successfully", "snackbar_train_location_saved", "snackbar_error_fetching_images", "snackbar_download_started", "snackbar_pdf_downloaded_successfully", "snackbar_could_not_launch", "snackbar_refresh_failed_e", "snackbar_return_gap_updated", "snackbar_data_not_refreshed", "snackbar_invalid_response_format", "snackbar_please_select_a_1", "snackbar_please_select_images", "snackbar_please_select_at", "snackbar_failed_to_upload", "snackbar_error_updating_issue", "snackbar_no_images_available", "snackbar_failed_to_fetch", "snackbar_error_fetching_trains", "snackbar_complaint_updated", "snackbar_update_failed", "snackbar_complaint_deleted_successfully", "snackbar_failed_to_delete", "snackbar_failed_to_submit", "snackbar_error_e", "snackbar_issues_saved_upload", "snackbar_feedback_deleted_successfully", "snackbar_error_deleting_feedback", "snackbar_job_chart_status", "snackbar_no_image_url", "snackbar_image_downloaded_successfully", "snackbar_failed_to_download", "snackbar_failed_to_download_1", "snackbar_otp_sent_successfully", "snackbar_failed_to_send", "snackbar_please_enter_the", "snackbar_email_saved_successfully", "snackbar_failed_to_verify", "snackbar_jobchart_deleted_successfully", "snackbar_failed_to_delete_1", "snackbar_failed_to_load", "text_welcome_to_railops", "text_sign_up_to_railops", "text_login_using_fingerprint", "text_logging_in_please_wait", "text_log_in", "text_home", "text_home_screen", "error_enter_mobile_number", "error_mobile_number_digits", "error_enter_password", "btn_log_in_with_mobile", "btn_new_user_sign_up", "text_privacy_policy", "text_terms_conditions", "text_know_about_app", "msg_login_successful", "msg_invalid_pin", "text_train_details", "text_please_enable_location_services", "text_location_permissions_required", "text_location_permission_denied_forever", "text_location_permissions_permanently_denied", "text_select_all", "text_location_permissions_error", "text_refresh_failed", "text_turn_on_location_services", "text_no_location_data_available", "text_edit_train_details", "text_search_train_number", "text_select_train_number", "text_monday", "text_tuesday", "text_wednesday", "text_thursday", "text_friday", "text_saturday", "text_sunday", "text_updating_data", "text_return_gap_updated_successfully", "text_train_details_updated_successfully", "text_restrictions", "text_location", "text_time", "text_enable_media_upload", "text_failed_to_load_zones", "text_failed_to_load_divisions", "text_failed_to_load_depots", "text_failed_to_load_train_details", "text_error_loading_train_data", "text_edit_profile", "text_error_loading_authentication_state", "text_verify_email_button", "text_first_name", "text_middle_name", "text_last_name", "text_employee_number", "text_role", "text_depot", "text_phone_number", "text_whatsapp_number", "text_secondary_phone_number", "text_email", "text_phone_number_must_be_10_digits", "text_phone_number_and_secondary_phone_number_must_be_different", "text_change_email", "text_change_password", "text_change_mobile_number", "text_change_whatsapp_number", "text_update_profile", "text_add_trains", "text_up", "text_down", "text_today", "text_yesterday", "text_failed_to_load_zones_add", "text_failed_to_load_divisions_add", "text_failed_to_load_depots_add", "text_please_enter_train_number", "text_train_number_min_digits", "text_please_select_return_gap", "text_coaches_sequence", "text_please_enter_train_name", "text_please_select_charting_day", "text_please_enter_from_station", "text_please_enter_to_station", "text_please_select_direction", "text_please_enter_start_time", "text_please_enter_end_time", "text_please_enter_charting_time", "text_please_enter_train_type", "text_add_new_user", "text_phone_and_secondary_phone_must_be_different", "text_validation_error", "text_submitting_data_please_wait", "text_please_complete_all_required_fields", "text_form_incomplete", "text_enter_first_name", "text_please_enter_first_name", "text_middle_name_optional", "text_enter_middle_name", "text_enter_last_name", "text_please_enter_last_name", "text_secondary_phone_number_optional", "text_whatsapp_number_same_as_phone", "text_use_same_number_for_whatsapp", "text_enter_10_digit_whatsapp_number", "text_please_enter_whatsapp_number", "text_whatsapp_number_must_be_10_digits", "text_please_enter_only_numbers", "text_request_for_add_user", "text_information_dialog_title", "text_please_complete_fields_in_order", "text_ok", "text_personal_information", "text_contact_details", "text_account_settings", "text_authentication_check_failed", "text_enter_a_valid_mobile_number", "text_user_data_not_found", "text_access_token_not_found", "text_update_user", "text_enter_mobile_number", "text_search_for_a_user_to_update_their_details", "text_user_not_found_please_check_the_mobile_number_and_try_again", "text_error_with_details", "text_change_mobile", "text_change_whatsapp", "text_change_your_email", "text_current_email", "text_new_email", "text_please_enter_new_email", "text_otp", "text_resend_in_seconds", "text_failed_to_send_otp", "text_failed_to_verify_otp", "text_please_enter_your_email", "text_please_enter_valid_email", "text_invalid_email_format", "text_change_your_password", "text_old_password", "text_new_password", "text_confirm_new_password", "text_please_enter_otp", "text_please_enter_value", "text_please_enter_valid_mobile", "text_change_your_mobile_number", "text_current_mobile_number", "text_new_mobile_number", "text_please_enter_new_mobile", "text_change_your_whatsapp_number", "text_current_whatsapp_number", "text_new_whatsapp_number", "text_please_enter_new_whatsapp", "text_please_enter_valid_mobile_number", "text_please_enter_new_mobile_number", "text_train", "text_coaches", "text_origin_date", "text_na", "text_do_you_want_to_logout", "text_yes", "text_no", "text_inside_train", "text_need_alarm", "text_deactivate", "text_logout", "text_email_otp_error", "text_phone_otp_error", "text_please_select_a_train_number", "text_add_update", "text_an_error_occurred_while_adding", "text_select_coaches_optional", "text_select_coaches_from_list", "text_please_select_date", "text_need_valid_email_before_deactivation", "text_send_otps_for_verification", "text_failed_to_send_otps", "text_enter_email_otp", "text_enter_phone_otp", "text_please_enter_field", "text_enable_fingerprint_login", "text_deactivate_account", "rm_feedback_app_bar_title", "rm_feedback_main_title", "form_resolved_status", "form_marks", "btn_validate", "btn_verified", "btn_verify_email", "btn_verify_otp", "btn_submit_feedback", "btn_upload_pnr_image", "btn_pick_media", "btn_camera", "btn_gallery", "btn_image", "btn_video", "btn_i_understand", "btn_ok", "status_verified", "status_pending", "status_completed", "text_email_verification_optional", "text_verified", "text_email_verification_info", "text_otp_sent_message", "validation_issue_type_required", "error_berth_number_invalid", "error_form_validation_failed", "error_max_videos", "error_max_videos_short", "error_max_feedback_images", "error_max_feedback_images_short", "error_max_images_reached", "error_picking_media", "error_email_required", "msg_email_verification_initiated", "error_otp_required", "msg_otp_verified", "error_something_wrong", "status_yes", "text_daily", "text_yes_lowercase", "text_no_lowercase", "error_berth_number_invalid_passenger", "error_no_train_selected", "error_fetching_charting_time", "dialog_train_not_running_content", "error_validating_pnr", "error_feedback_submission_passenger", "error_max_images_reached_passenger", "error_picking_media_passenger", "error_generic_passenger", "error_something_wrong_passenger", "text_check_inbox", "text_check_spam", "text_add_safe_sender", "status_no", "status_select", "section_email_verification", "section_selected_images", "section_selected_videos", "dialog_email_verification_info", "dialog_select_media_type", "validation_fill_all_fields", "validation_pnr_digits", "validation_berth_number", "validation_feedback_length", "validation_email_required", "validation_otp_required", "validation_train_no_required", "validation_train_name_required", "validation_passenger_name_required", "validation_mobile_required", "validation_mobile_digits", "validation_sub_issue_required", "validation_resolved_required", "validation_marks_required", "msg_pnr_images_limit", "msg_feedback_images_limit", "msg_images_added_limit", "msg_error_picking_media", "msg_failed_fetch_train_name", "msg_invalid_pnr", "msg_pnr_success", "msg_pnr_validation_failed", "msg_email_verification_sent", "msg_feedback_submitted", "msg_feedback_failed", "msg_unexpected_error", "info_spam_folder_note", "info_after_requesting_otp", "info_check_inbox", "info_check_spam", "info_add_safe_sender", "text_no_feedback_images", "text_no_pnr_images", "text_character_count", "loading_sending_otp", "loading_verifying_otp", "loading_submitting_feedback", "msg_otp_sent_check_folders", "msg_error_generic", "msg_something_went_wrong", "status_yes_lowercase", "status_no_lowercase", "passenger_feedback_app_bar_title", "passenger_feedback_main_title", "form_train_name_readonly", "hint_search_train_number", "category_ac", "category_nonac", "category_tt", "category_rm", "rating_excellent", "rating_very_good", "rating_good", "rating_average", "rating_poor", "feedback_table_instruction", "feedback_table_item_header", "dialog_train_not_running", "msg_train_not_running_details", "msg_running_days_daily", "msg_running_days_na", "validation_provide_rating", "msg_rm_fields_required", "msg_upload_complete_wait", "msg_videos_limit", "msg_videos_limit_reached", "feedback_ac_item_1", "feedback_ac_item_2", "feedback_ac_item_3", "feedback_ac_item_4", "feedback_ac_item_5", "feedback_nonac_item_1", "feedback_nonac_item_2", "feedback_nonac_item_3", "button_ok", "error_fetch_train_name", "error_invalid_pnr", "success_pnr_fetched", "error_pnr_validation", "success_feedback_submitted", "error_feedback_submission", "error_unexpected", "email_verification_info_title", "email_verification_spam_notice", "email_verification_after_otp", "email_verification_understand", "success_email_verification", "error_prefix", "success_otp_verified", "upload_pnr_image", "selected_images", "no_pnr_images_selected", "upload_limit_pnr_3", "media_source_camera", "media_source_gallery", "upload_limit_pnr_max", "upload_limit_reached_message", "error_picking_media_simple", "pick_images_videos_feedback", "no_feedback_images_selected", "selected_videos", "select_media_type", "media_type_image", "media_type_video", "upload_limit_videos_3", "upload_limit_videos_max", "upload_limit_feedback_images_3", "upload_limit_feedback_images_max", "text_pnr_document", "text_feedback_media", "loading_deleting_feedback", "success_feedback_deleted", "error_deleting_feedback", "text_train_no", "text_not_verified", "btn_review", "btn_retry", "msg_no_feedback_available", "dialog_title_review_feedback", "text_status_pending_editable", "text_status_completed_not_editable", "text_pnr_documents", "text_failed_to_load_image", "text_tap_to_play_video", "form_remarks_hint", "form_resolved_yes_no", "form_marks_1_to_10", "status_none_selected", "btn_update", "validation_select_issue_type", "validation_select_sub_issue_type", "validation_select_resolved_status", "validation_select_marks", "validation_enter_valid_email", "validation_enter_otp", "validation_remarks_max_100", "msg_cannot_verify_email_completed", "msg_cannot_verify_otp_completed", "msg_otp_verified_successfully", "msg_feedback_updated_successfully", "msg_failed_to_update_feedback", "msg_failed_to_load_video", "msg_error_with_details", "msg_error_or_something_wrong", "loading_updating_feedback", "text_character_count_100", "dialog_email_verification_info_title", "dialog_email_verification_spam_notice", "dialog_after_requesting_otp", "dialog_check_inbox_first", "dialog_check_spam_folder", "dialog_add_safe_sender", "text_otp_sent_check_folders", "form_issue_type_label", "form_sub_issue_type_label", "form_task_status_label", "status_pending_option", "status_completed_option", "text_need_help_reach_out", "error_could_not_launch_phone", "text_notifications", "tooltip_test_notifications", "tooltip_notification_settings", "tooltip_mark_all_as_read", "tooltip_clear_all_notifications", "text_no_notifications", "dialog_title_clear_notifications", "dialog_content_clear_notifications", "btn_clear", "text_notification_fallback", "text_coach_details", "table_header_station", "table_header_coach", "table_header_onboard", "table_header_deboard", "table_header_vacant", "text_notification_system_tests", "text_notification_system_testing", "text_notification_test_description", "text_ready_to_run_notification_tests", "btn_run_all_tests", "btn_running_tests", "btn_individual_tests", "text_test_results", "text_no_test_results_yet", "text_run_tests_to_see_results", "text_running_comprehensive_tests", "text_all_tests_completed_successfully", "text_some_tests_failed", "text_test_execution_failed", "dialog_title_select_test", "test_fcm_token_generation", "test_firestore_token_storage", "test_complete_notification_sync", "test_cloud_function_endpoint", "test_enhanced_notification", "test_debug_firestore_token_storage", "test_force_token_refresh", "test_quick_token_check", "text_running_test", "text_test_completed_successfully", "text_test_failed", "text_no_test_data_available", "text_test_passed", "text_test_failed_label", "rail_sathi_app_bar_title", "tab_write_complaint", "tab_view_complaints", "error_could_not_launch_link", "error_failed_to_load_issues", "error_invalid_response_format", "error_failed_to_load_trip_report", "msg_data_refreshed_successfully", "error_refresh_failed", "error_select_train_date_first", "text_no_coaches_found", "text_please_select_train_date", "text_coach_label", "text_no_issues_reported", "text_reported_issues", "text_unknown_issue", "btn_manage_issues", "text_train_rake_deficiency_report", "text_rake_deficiency_report_issues", "tooltip_upload_coach_image", "btn_upload_image_video", "btn_submit_issue", "form_pnr", "form_search_by_train_number_or_name", "form_dd_mm_yyyy", "btn_next", "tab_cleaning_issues", "tab_linen_related_issues", "msg_please_fill_all_required_details", "text_ehk_not_assigned", "msg_pnr_validation_error_e", "msg_are_you_sure_delete_image", "text_selected_date", "text_date", "text_pnr", "msg_are_you_sure_delete_complaint", "reports_app_bar_title", "btn_attendance_report", "btn_round_rake_deficiency_report", "btn_train_report", "btn_detailed_attendance_report", "btn_detailed_round_trip_attendance_report", "btn_obhs_to_mcc_handover", "btn_mcc_to_obhs_handover", "btn_monthly_attendance_report", "btn_monthly_round_trip_attendance_report", "text_daily_reports", "text_monthly_reports", "btn_show_all", "btn_show_less", "section_daily_reports", "section_monthly_reports", "section_attendance_for", "report_attendance", "report_detailed_attendance", "report_detailed_round_trip", "report_obhs_to_mcc_handover", "report_monthly_attendance", "report_monthly_round_trip", "report_monthly_with_mobile", "text_notification_settings", "text_save_settings", "text_onboarding_notifications", "text_enable_onboarding_notifications", "text_station_approach_alerts", "text_boarding_alerts", "text_off_boarding_alerts", "text_proximity_alerts", "text_timing_settings", "text_advance_notice_minutes", "text_proximity_threshold_km", "text_coach_filters", "text_enable_coach_specific_filtering", "text_enabled_coach_types", "text_sound_vibration", "text_enable_sound", "text_enable_vibration", "text_advanced_settings", "text_background_notifications", "text_location_based_notifications", "text_max_notifications_per_hour", "msg_notification_preferences_saved", "msg_error_saving_preferences", "error_fetching_trains", "form_mobile", "form_enter_train_number", "form_description", "dropdown_other", "dropdown_cleaning", "dropdown_linen", "dropdown_pending", "dropdown_completed", "dialog_delete_image", "dialog_delete_image_confirm", "dialog_delete_complaint", "dialog_delete_complaint_confirm", "btn_add_image", "btn_save_changes", "btn_edit", "btn_select_date", "text_edit_complaint", "form_train_label", "error_failed_to_fetch_complaints", "text_berth_no", "msg_image_deleted", "msg_complaint_updated", "msg_update_failed", "msg_complaint_deleted_success", "msg_failed_to_delete_complaint", "snackbar_image_deleted", "snackbar_failed_to_delete_image", "snackbar_failed_to_delete_complaint", "snackbar_failed_to_fetch_complaints", "btn_rake_deficiency_report_simple", "btn_monthly_with_mobile", "btn_monthly_without_mobile", "text_info", "text_reports_for", "text_trains_not_running", "text_train_not_running", "text_please_select_relevant_trains", "text_following_trains_not_running", "text_train_label", "text_running_days_label", "text_running_trains_on", "text_train_not_running_message", "pnr_status_title", "hint_enter_pnr_number", "label_pnr_number", "btn_check_pnr", "text_no_pnr_data_found", "error_invalid_pnr_number", "error_failed_fetch_pnr", "label_train_number", "label_train_name", "label_boarding_date", "label_from", "label_to", "label_class", "label_departure", "label_arrival", "label_overall_status", "label_booking_date", "label_passenger", "label_coach_berth", "label_ehk_users", "label_ca_users", "label_obhs_users"], "hi": ["text_requested_users", "text_end_date_cannot", "text_please_select_both", "text_update_user_details", "text_request_for_update", "text_i_dont_have", "text_information", "text_request_for_sign", "text_forgotten_password", "text_mobile_otp_login", "text_enter_otp", "text_otp_sent_to_mobile", "text_profile", "text_sign_in_with", "text_new_user_sign", "text_please_enter_a", "text_failed_to_fetch", "text_check_pnr_status", "text_pnr_number", "text_no_pnr_data", "text_please_turn_on", "text_an_error_occurred", "text_your_current_location", "text_location_services_disabled", "text_please_enable_location", "text_location_permission_denied", "text_open_settings", "text_location_permission_denied_1", "text_refresh_failed_e", "text_no_location_data", "text_no_data_available_1", "text_no_train_details", "text_none", "text_download_pdf_for", "text_mail_pdf_for", "text_add_configuration", "text_select_charting_day", "text_submitting", "text_return_gap_updated", "text_data_not_refreshed", "text_add", "text_train_details_updated", "text_add_train_details", "text_add_train", "text_stoppages_in_sequence", "text_submitting_train_details", "text_edit_configuration", "text_no_coaches_available", "text_invalid_response_format", "text_please_select_a_2", "text_coach_handover_report", "text_save_selection", "text_select_media_type", "text_image", "text_video", "text_please_select_images", "text_please_select_at", "text_failed_to_upload", "text_error_updating_issue", "text_statustype_by", "text_no_images_available", "text_issuesubissue", "text_status", "text_submitupdate", "text_reportedby", "text_fixedby", "text_resolvedby", "text_pick_images", "text_uploading", "text_submit_upload", "text_retry", "text_approve", "text_no_requests_selected", "text_are_you_sure", "text_requeststoprocesslength_users_approved", "text_confirm_denial", "text_deny_request", "text_approve_selected", "text_approve_all", "text_processing_requests", "text_clear_search", "text_failed_to_fetch_1", "text_error_fetching_trains", "text_traintrainno_traintrainname", "text_other", "text_existing_images", "text_delete_image", "text_image_deleted", "text_newly_selected_images", "text_add_image", "text_complaint_updated", "text_update_failed", "text_save_changes", "text_delete_complaint", "text_are_you_sure_1", "text_complaint_deleted_successfully", "text_failed_to_delete", "text_no_complaints_found", "text_train_no_complainttrainnumber", "text_date_complaintcomplaindate", "text_pnr_complaintpnrnumber", "text_edit", "text_complaint_submitted_successfully", "text_failed_to_submit", "text_error_e", "text_ehk_ehkdisplay", "text_pending", "text_completed", "text_upload_imagevideo", "text_submit_issue", "text_validate", "text_error_loading_authentication", "text_storage_permission_required", "text_please_select_a_3", "text_error_json_file", "text_selection_cleared", "text_upload_json_data", "text_update", "text_add_issue", "text_no_subissues", "text_select_issue", "text_add_subissue", "text_add_new_item", "text_images_or_videos", "text_please_select_an", "text_issues_saved_upload", "text_select_issues_for", "text_pick_imagesvideos", "text_please_select_imagevideo", "text_confirm_deletion", "text_delete_report", "text_coach_issue_status", "text_both_person_and", "text_select_widgetstatustype_by", "text_subissue_widgetname", "text_issue_widgetname", "text_confirm", "text_manage_issues", "text_rake_deficiency_report", "text_upload_pnr_image", "text_pick_imagesvideos_for", "text_please_wait_until", "text_submit_feedback", "text_verify_email", "text_check_your_inbox", "text_if_not_found", "text_add_our_domain", "text_i_understand", "text_nonac", "text_select", "text_failed_to_load", "text_review_feedback", "text_deleting_feedback", "text_feedback_deleted_successfully", "text_error_deleting_feedback", "text_no_feedback_available", "text_train_no_trainnumber", "text_non_ac", "text_message", "text_job_chart_status", "text_please_select_all", "text_update_amount_for", "text_assigned", "text_amount", "text_no_image_url", "text_image_downloaded_successfully", "text_failed_to_download", "text_failed_to_download_1", "text_image_detail", "text_download_image", "text_train_trainnumber_details", "text_confirm_deactivation", "text_proceed", "text_add_email", "text_email_verification", "text_phone_verification", "text_logout_confirmation", "text_do_you_want", "text_addupdate", "text_otp_sent_successfully", "text_failed_to_send", "text_please_enter_the", "text_failed_to_verify", "text_uploaded_at_formatteddate", "text_uploaded_by_widgetimageresponsecreatedby", "text_id_widgetimageresponseid", "text_coach_widgetimageresponsecoach", "text_issue_widgetimageresponseissue", "text_delete_confirmation", "text_are_you_sure_2", "text_save", "text_ehkca", "text_image_upload_initiated", "text_failed_to_upload_1", "text_please_select_an_1", "text_jobchart_deleted_successfully", "text_failed_to_delete_1", "text_pick_image", "text_upload_image", "text_failed_to_load_1", "text_language", "text_select_language", "text_rail_sathi", "title_location_access_required", "title_upload_status", "title_compressing_image", "title_upload_entrykeysubstring0_6", "title_permission_denied", "title_confirm_delete", "title_add_new_item", "title_add_issue", "title_add_subissue", "title_select_widgetstatustype_by", "title_update_amount_for", "form_select_date_ddmmmyyyy", "form_first_name", "form_enter_first_name", "form_middle_name_optional", "form_enter_middle_name", "form_last_name", "form_enter_last_name", "form_phone_number", "form_secondary_phone_number", "form_whatsapp_number", "form_enter_10digit_whatsapp", "form_email", "form_enter_your_email", "form_enter_your_first", "form_enter_your_middle", "form_enter_your_last", "form_enter_10digit_secondary", "form_email_1", "form_enter_your_10digit", "form_select_train_numbers", "form_zone", "form_zone_1", "form_employee_id", "form_depot", "form_reenter_password", "form_divisions", "form_divisions_1", "form_select_coaches", "form_middle_name", "form_select_train_number", "form_select_stations", "form_whatsapp_number_1", "form_enter_secondary_phone", "form_mobile_number_1", "form_enter_mobile_number", "form_enter_mobile_number_1", "form_related_train", "form_division", "form_depot_1", "form_charting_day", "form_from_station", "form_to_station", "form_direction_updown", "form_start_time", "form_eg_0900_am", "form_end_time", "form_eg_0500_pm", "form_charting_time", "form_return_gap_days", "form_inout", "form_related_train_number", "form_updown", "form_train_type", "form_search_train_number", "form_coaches_comma_separated", "form_eg_h_gsl", "form_enter_coach_names", "form_select_days", "form_type_a_station", "form_search", "form_stoppages_in_sequence", "form_type_a_station_1", "form_frequency", "form_enter_new_coach", "form_use_comma_to", "form_search_by_name", "form_train", "form_complaint_type", "form_status", "form_write_your_issue", "form_issue_status", "form_name", "form_search_by_train", "form_train_selection", "form_journey_start_date", "form_ddmmyyyy", "form_berth", "form_issue_name", "form_subissue_name", "form_search_1", "form_widgetstatustype_by", "form_select_date_time", "form_search_train_number_1", "form_train_number_1", "form_enter_otp", "form_resolved_yesno", "form_train_name_1", "form_marks_1_to", "form_remarks_by_passenger", "form_passenger_name_1", "form_pnr_number_1", "form_crn_number_1", "form_coach_no_1", "form_berth_no_1", "form_task_status_1", "form_feedback", "form_amount_in_hand", "form_select_user", "form_enter_email_otp", "form_enter_phone_otp", "form_select_coaches_optional", "btn_statustype_by", "btn_traintrainno_traintrainname", "snackbar_please_select_a", "snackbar_data_refreshed_successfully", "snackbar_train_location_saved", "snackbar_error_fetching_images", "snackbar_download_started", "snackbar_pdf_downloaded_successfully", "snackbar_could_not_launch", "snackbar_refresh_failed_e", "snackbar_return_gap_updated", "snackbar_data_not_refreshed", "snackbar_invalid_response_format", "snackbar_please_select_a_1", "snackbar_please_select_images", "snackbar_please_select_at", "snackbar_failed_to_upload", "snackbar_error_updating_issue", "snackbar_no_images_available", "snackbar_failed_to_fetch", "snackbar_error_fetching_trains", "snackbar_complaint_updated", "snackbar_update_failed", "snackbar_complaint_deleted_successfully", "snackbar_failed_to_delete", "snackbar_failed_to_submit", "snackbar_error_e", "snackbar_issues_saved_upload", "snackbar_feedback_deleted_successfully", "snackbar_error_deleting_feedback", "snackbar_job_chart_status", "snackbar_no_image_url", "snackbar_image_downloaded_successfully", "snackbar_failed_to_download", "snackbar_failed_to_download_1", "snackbar_otp_sent_successfully", "snackbar_failed_to_send", "snackbar_please_enter_the", "snackbar_email_saved_successfully", "snackbar_failed_to_verify", "snackbar_jobchart_deleted_successfully", "snackbar_failed_to_delete_1", "snackbar_failed_to_load", "text_please_enable_location_services", "text_location_permissions_required", "text_location_permission_denied_forever", "text_location_permissions_permanently_denied", "text_select_all", "text_location_permissions_error", "text_refresh_failed", "text_turn_on_location_services", "text_no_location_data_available", "text_edit_train_details", "text_search_train_number", "text_select_train_number", "text_monday", "text_tuesday", "text_wednesday", "text_thursday", "text_friday", "text_saturday", "text_sunday", "text_updating_data", "text_return_gap_updated_successfully", "text_train_details_updated_successfully", "text_restrictions", "text_location", "text_time", "text_enable_media_upload", "text_failed_to_load_zones", "text_failed_to_load_divisions", "text_failed_to_load_depots", "text_failed_to_load_train_details", "text_error_loading_train_data", "text_edit_profile", "text_error_loading_authentication_state", "text_verify_email_button", "text_first_name", "text_middle_name", "text_last_name", "text_employee_number", "text_role", "text_depot", "text_phone_number", "text_whatsapp_number", "text_secondary_phone_number", "text_email", "text_phone_number_must_be_10_digits", "text_phone_number_and_secondary_phone_number_must_be_different", "text_change_email", "text_change_mobile_number", "text_change_whatsapp_number", "text_update_profile", "text_add_trains", "text_today", "text_yesterday", "text_failed_to_load_zones_add", "text_failed_to_load_divisions_add", "text_failed_to_load_depots_add", "text_please_enter_train_number", "text_train_number_min_digits", "text_please_select_return_gap", "text_coaches_sequence", "text_please_enter_train_name", "text_please_select_charting_day", "text_please_enter_from_station", "text_please_enter_to_station", "text_please_select_direction", "text_please_enter_start_time", "text_please_enter_end_time", "text_please_enter_charting_time", "text_please_enter_train_type", "text_add_new_user", "text_phone_and_secondary_phone_must_be_different", "text_validation_error", "text_submitting_data_please_wait", "text_please_complete_all_required_fields", "text_form_incomplete", "text_enter_first_name", "text_please_enter_first_name", "text_middle_name_optional", "text_enter_middle_name", "text_enter_last_name", "text_please_enter_last_name", "text_secondary_phone_number_optional", "text_whatsapp_number_same_as_phone", "text_use_same_number_for_whatsapp", "text_enter_10_digit_whatsapp_number", "text_please_enter_whatsapp_number", "text_whatsapp_number_must_be_10_digits", "text_please_enter_only_numbers", "text_request_for_add_user", "text_information_dialog_title", "text_please_complete_fields_in_order", "text_personal_information", "text_contact_details", "text_account_settings", "text_authentication_check_failed", "text_enter_a_valid_mobile_number", "text_user_data_not_found", "text_access_token_not_found", "text_update_user", "text_enter_mobile_number", "text_search_for_a_user_to_update_their_details", "text_user_not_found_please_check_the_mobile_number_and_try_again", "text_error_with_details", "text_please_enter_your_email", "text_please_enter_valid_email", "text_invalid_email_format", "text_please_enter_valid_mobile_number", "text_please_enter_new_mobile_number", "text_do_you_want_to_logout", "text_yes", "text_no", "text_inside_train", "text_need_alarm", "text_deactivate", "text_logout", "text_email_otp_error", "text_phone_otp_error", "text_please_select_a_train_number", "text_add_update", "text_an_error_occurred_while_adding", "text_select_coaches_optional", "text_select_coaches_from_list", "text_please_select_date", "text_need_valid_email_before_deactivation", "text_send_otps_for_verification", "text_failed_to_send_otps", "text_enter_email_otp", "text_enter_phone_otp", "text_please_enter_field", "text_enable_fingerprint_login", "text_deactivate_account", "text_email_verification_optional", "text_verified", "text_email_verification_info", "text_otp_sent_message", "error_berth_number_invalid", "error_form_validation_failed", "error_max_videos", "error_max_videos_short", "error_max_feedback_images", "error_max_feedback_images_short", "error_max_images_reached", "error_picking_media", "error_email_required", "msg_email_verification_initiated", "error_otp_required", "error_something_wrong", "text_daily", "text_yes_lowercase", "text_no_lowercase", "error_berth_number_invalid_passenger", "error_no_train_selected", "error_fetching_charting_time", "dialog_train_not_running_content", "error_validating_pnr", "error_feedback_submission_passenger", "error_max_images_reached_passenger", "error_picking_media_passenger", "error_generic_passenger", "error_something_wrong_passenger", "text_check_inbox", "text_check_spam", "text_add_safe_sender", "msg_otp_sent_check_folders", "msg_error_generic", "msg_something_went_wrong", "status_yes_lowercase", "status_no_lowercase", "passenger_feedback_app_bar_title", "passenger_feedback_main_title", "form_train_name_readonly", "hint_search_train_number", "category_ac", "category_nonac", "category_tt", "category_rm", "rating_excellent", "rating_very_good", "rating_good", "rating_average", "rating_poor", "feedback_table_instruction", "feedback_table_item_header", "dialog_train_not_running", "msg_train_not_running_details", "msg_running_days_daily", "msg_running_days_na", "validation_provide_rating", "msg_rm_fields_required", "msg_upload_complete_wait", "msg_videos_limit", "msg_videos_limit_reached", "feedback_ac_item_1", "feedback_ac_item_2", "feedback_ac_item_3", "feedback_ac_item_4", "feedback_ac_item_5", "feedback_nonac_item_1", "feedback_nonac_item_2", "feedback_nonac_item_3", "button_ok", "error_fetch_train_name", "error_invalid_pnr", "success_pnr_fetched", "error_pnr_validation", "success_feedback_submitted", "error_feedback_submission", "error_unexpected", "email_verification_info_title", "email_verification_spam_notice", "email_verification_after_otp", "email_verification_understand", "success_email_verification", "error_prefix", "success_otp_verified", "upload_pnr_image", "selected_images", "no_pnr_images_selected", "upload_limit_pnr_3", "media_source_camera", "media_source_gallery", "upload_limit_pnr_max", "upload_limit_reached_message", "error_picking_media_simple", "pick_images_videos_feedback", "no_feedback_images_selected", "selected_videos", "select_media_type", "media_type_image", "media_type_video", "upload_limit_videos_3", "upload_limit_videos_max", "upload_limit_feedback_images_3", "upload_limit_feedback_images_max", "text_pnr_document", "text_feedback_media", "loading_deleting_feedback", "success_feedback_deleted", "error_deleting_feedback", "text_train_no", "text_not_verified", "btn_review", "msg_no_feedback_available", "dialog_title_review_feedback", "text_status_pending_editable", "text_status_completed_not_editable", "text_pnr_documents", "text_failed_to_load_image", "text_tap_to_play_video", "form_remarks_hint", "form_resolved_yes_no", "form_marks_1_to_10", "status_none_selected", "validation_select_issue_type", "validation_select_sub_issue_type", "validation_select_resolved_status", "validation_select_marks", "validation_enter_valid_email", "validation_enter_otp", "validation_remarks_max_100", "msg_cannot_verify_email_completed", "msg_cannot_verify_otp_completed", "msg_otp_verified_successfully", "msg_feedback_updated_successfully", "msg_failed_to_update_feedback", "msg_failed_to_load_video", "msg_error_with_details", "msg_error_or_something_wrong", "loading_updating_feedback", "text_character_count_100", "dialog_email_verification_info_title", "dialog_email_verification_spam_notice", "dialog_after_requesting_otp", "dialog_check_inbox_first", "dialog_check_spam_folder", "dialog_add_safe_sender", "text_otp_sent_check_folders", "form_issue_type_label", "form_sub_issue_type_label", "form_task_status_label", "status_pending_option", "status_completed_option", "text_need_help_reach_out", "error_could_not_launch_phone", "tooltip_test_notifications", "tooltip_notification_settings", "tooltip_mark_all_as_read", "tooltip_clear_all_notifications", "dialog_title_clear_notifications", "dialog_content_clear_notifications", "btn_clear", "text_notification_fallback", "table_header_station", "table_header_coach", "table_header_onboard", "table_header_deboard", "table_header_vacant", "text_notification_system_tests", "text_notification_system_testing", "text_notification_test_description", "text_ready_to_run_notification_tests", "btn_run_all_tests", "btn_running_tests", "btn_individual_tests", "text_test_results", "text_no_test_results_yet", "text_run_tests_to_see_results", "text_running_comprehensive_tests", "text_all_tests_completed_successfully", "text_some_tests_failed", "text_test_execution_failed", "dialog_title_select_test", "test_fcm_token_generation", "test_firestore_token_storage", "test_complete_notification_sync", "test_cloud_function_endpoint", "test_enhanced_notification", "test_debug_firestore_token_storage", "test_force_token_refresh", "test_quick_token_check", "text_running_test", "text_test_completed_successfully", "text_test_failed", "text_no_test_data_available", "text_test_passed", "text_test_failed_label", "rail_sathi_app_bar_title", "tab_write_complaint", "tab_view_complaints", "error_could_not_launch_link", "error_failed_to_load_issues", "error_invalid_response_format", "error_failed_to_load_trip_report", "msg_data_refreshed_successfully", "error_refresh_failed", "error_select_train_date_first", "text_no_coaches_found", "text_please_select_train_date", "text_coach_label", "text_no_issues_reported", "text_reported_issues", "text_unknown_issue", "btn_manage_issues", "text_train_rake_deficiency_report", "text_rake_deficiency_report_issues", "tooltip_upload_coach_image", "btn_upload_image_video", "btn_submit_issue", "form_pnr", "form_search_by_train_number_or_name", "form_dd_mm_yyyy", "btn_next", "tab_cleaning_issues", "tab_linen_related_issues", "msg_please_fill_all_required_details", "text_ehk_not_assigned", "msg_pnr_validation_error_e", "msg_are_you_sure_delete_image", "text_selected_date", "text_date", "text_pnr", "msg_are_you_sure_delete_complaint", "reports_app_bar_title", "btn_attendance_report", "btn_round_rake_deficiency_report", "btn_train_report", "btn_detailed_attendance_report", "btn_detailed_round_trip_attendance_report", "btn_obhs_to_mcc_handover", "btn_mcc_to_obhs_handover", "btn_monthly_attendance_report", "btn_monthly_round_trip_attendance_report", "text_daily_reports", "text_monthly_reports", "btn_show_all", "btn_show_less", "section_daily_reports", "section_monthly_reports", "section_attendance_for", "report_attendance", "report_detailed_attendance", "report_detailed_round_trip", "report_obhs_to_mcc_handover", "report_monthly_attendance", "report_monthly_round_trip", "report_monthly_with_mobile", "text_save_settings", "text_onboarding_notifications", "text_enable_onboarding_notifications", "text_station_approach_alerts", "text_boarding_alerts", "text_off_boarding_alerts", "text_proximity_alerts", "text_timing_settings", "text_advance_notice_minutes", "text_proximity_threshold_km", "text_coach_filters", "text_enable_coach_specific_filtering", "text_enabled_coach_types", "text_sound_vibration", "text_enable_sound", "text_enable_vibration", "text_advanced_settings", "text_background_notifications", "text_location_based_notifications", "text_max_notifications_per_hour", "msg_notification_preferences_saved", "msg_error_saving_preferences", "error_fetching_trains", "form_mobile", "form_description", "dropdown_other", "dropdown_cleaning", "dropdown_linen", "dropdown_pending", "dropdown_completed", "dialog_delete_image", "dialog_delete_image_confirm", "dialog_delete_complaint", "dialog_delete_complaint_confirm", "btn_add_image", "btn_save_changes", "btn_edit", "btn_select_date", "text_edit_complaint", "form_train_label", "error_failed_to_fetch_complaints", "text_berth_no", "msg_image_deleted", "msg_complaint_updated", "msg_update_failed", "msg_complaint_deleted_success", "msg_failed_to_delete_complaint", "snackbar_image_deleted", "snackbar_failed_to_delete_image", "snackbar_failed_to_delete_complaint", "snackbar_failed_to_fetch_complaints", "btn_rake_deficiency_report_simple", "btn_monthly_with_mobile", "btn_monthly_without_mobile", "text_reports_for", "text_trains_not_running", "text_train_not_running", "text_please_select_relevant_trains", "text_following_trains_not_running", "text_train_label", "text_running_days_label", "text_running_trains_on", "text_train_not_running_message", "pnr_status_title", "hint_enter_pnr_number", "label_pnr_number", "btn_check_pnr", "text_no_pnr_data_found", "error_invalid_pnr_number", "error_failed_fetch_pnr", "label_train_number", "label_train_name", "label_boarding_date", "label_from", "label_to", "label_class", "label_departure", "label_arrival", "label_overall_status", "label_booking_date", "label_passenger", "label_coach_berth", "label_ehk_users", "label_ca_users", "label_obhs_users"], "kn": ["text_reenter_otp", "text_deny", "text_enable", "text_location_access_required", "text_decline", "text_accept", "text_confirm_delete", "text_delete", "text_storage_permission_is", "text_please_select_a", "text_update_required", "text_update_now", "text_please_select_a_1", "text_all_details_updated", "text_failed_to_update", "text_data_refreshed_successfully", "text_train_location_saved", "text_self", "text_other_ca", "text_other_ehkobhs", "text_chart_has_not", "text_camera", "text_gallery", "text_getting_location", "text_error_message", "text_failed_to_get", "text_error_fetching_images", "text_back_to_all", "text_submit", "text_upload_status", "text_compressing_image", "text_upload_entrykeysubstring0_6", "text_image_uploading", "text_error_snapshoterror", "text_latitude_entrylatitude", "text_longitude_entrylongitude", "text_distance_entrydistance_km", "text_updated_by_entryupdatedby", "text_no_data_available", "text_show_more", "text_show_less", "text_could_not_open", "text_download_started", "text_pdf_downloaded_successfully", "text_download", "text_get_in_email", "text_could_not_launch", "text_permission_denied", "text_requested_users", "text_end_date_cannot", "text_please_select_both", "text_update_user_details", "text_request_for_update", "text_i_dont_have", "text_information", "text_request_for_sign", "text_forgot_password", "text_forgotten_password", "text_mobile_otp_login", "text_enter_otp", "text_otp_sent_to_mobile", "text_profile", "text_error", "text_sign_in_with", "text_new_user_sign", "text_please_enter_a", "text_failed_to_fetch", "text_check_pnr_status", "text_pnr_number", "text_no_pnr_data", "text_please_turn_on", "text_an_error_occurred", "text_your_current_location", "text_location_services_disabled", "text_please_enable_location", "text_location_permission_denied", "text_open_settings", "text_location_permission_denied_1", "text_refresh_failed_e", "text_no_location_data", "text_no_data_available_1", "text_no_train_details", "text_none", "text_download_pdf_for", "text_mail_pdf_for", "text_add_configuration", "text_select_charting_day", "text_submitting", "text_return_gap_updated", "text_data_not_refreshed", "text_add", "text_train_details_updated", "text_add_train_details", "text_add_train", "text_stoppages_in_sequence", "text_submitting_train_details", "text_edit_configuration", "text_no_coaches_available", "text_invalid_response_format", "text_please_select_a_2", "text_coach_handover_report", "text_save_selection", "text_select_media_type", "text_image", "text_video", "text_please_select_images", "text_please_select_at", "text_failed_to_upload", "text_error_updating_issue", "text_statustype_by", "text_no_images_available", "text_issuesubissue", "text_status", "text_submitupdate", "text_reportedby", "text_fixedby", "text_resolvedby", "text_pick_images", "text_uploading", "text_submit_upload", "text_retry", "text_approve", "text_no_requests_selected", "text_are_you_sure", "text_requeststoprocesslength_users_approved", "text_confirm_denial", "text_deny_request", "text_approve_selected", "text_approve_all", "text_processing_requests", "text_clear_search", "text_failed_to_fetch_1", "text_error_fetching_trains", "text_traintrainno_traintrainname", "text_other", "text_existing_images", "text_delete_image", "text_image_deleted", "text_newly_selected_images", "text_add_image", "text_complaint_updated", "text_update_failed", "text_save_changes", "text_delete_complaint", "text_are_you_sure_1", "text_complaint_deleted_successfully", "text_failed_to_delete", "text_select_date", "text_no_complaints_found", "text_train_no_complainttrainnumber", "text_date_complaintcomplaindate", "text_pnr_complaintpnrnumber", "text_edit", "text_complaint_submitted_successfully", "text_failed_to_submit", "text_error_e", "text_ehk_ehkdisplay", "text_pending", "text_completed", "text_upload_imagevideo", "text_submit_issue", "text_validate", "text_next", "text_error_loading_authentication", "text_storage_permission_required", "text_please_select_a_3", "text_error_json_file", "text_selection_cleared", "text_upload_json_data", "text_update", "text_add_issue", "text_no_subissues", "text_select_issue", "text_add_subissue", "text_add_new_item", "text_images_or_videos", "text_please_select_an", "text_issues_saved_upload", "text_select_issues_for", "text_pick_imagesvideos", "text_please_select_imagevideo", "text_confirm_deletion", "text_delete_report", "text_coach_issue_status", "text_both_person_and", "text_select_widgetstatustype_by", "text_subissue_widgetname", "text_issue_widgetname", "text_confirm", "text_manage_issues", "text_rake_deficiency_report", "text_upload_pnr_image", "text_pick_imagesvideos_for", "text_please_wait_until", "text_submit_feedback", "text_verify_email", "text_check_your_inbox", "text_if_not_found", "text_add_our_domain", "text_i_understand", "text_nonac", "text_select", "text_failed_to_load", "text_review_feedback", "text_deleting_feedback", "text_feedback_deleted_successfully", "text_error_deleting_feedback", "text_no_feedback_available", "text_train_no_trainnumber", "text_non_ac", "text_message", "text_job_chart_status", "text_please_select_all", "text_update_amount_for", "text_assigned", "text_amount", "text_no_image_url", "text_image_downloaded_successfully", "text_failed_to_download", "text_failed_to_download_1", "text_image_detail", "text_download_image", "text_train_trainnumber_details", "text_confirm_deactivation", "text_proceed", "text_add_email", "text_back", "text_email_verification", "text_phone_verification", "text_logout_confirmation", "text_do_you_want", "text_addupdate", "text_otp_sent_successfully", "text_failed_to_send", "text_please_enter_the", "text_failed_to_verify", "text_uploaded_at_formatteddate", "text_uploaded_by_widgetimageresponsecreatedby", "text_id_widgetimageresponseid", "text_coach_widgetimageresponsecoach", "text_issue_widgetimageresponseissue", "text_delete_confirmation", "text_are_you_sure_2", "text_save", "text_ehkca", "text_image_upload_initiated", "text_failed_to_upload_1", "text_please_select_an_1", "text_jobchart_deleted_successfully", "text_failed_to_delete_1", "text_pick_image", "text_upload_image", "text_failed_to_load_1", "text_language", "text_select_language", "text_rail_sathi", "title_location_access_required", "title_upload_status", "title_compressing_image", "title_upload_entrykeysubstring0_6", "title_permission_denied", "title_confirm_delete", "title_add_new_item", "title_add_issue", "title_add_subissue", "title_select_widgetstatustype_by", "title_update_amount_for", "form_train_number", "form_date", "form_select_date_ddmmmyyyy", "form_first_name", "form_enter_first_name", "form_middle_name_optional", "form_enter_middle_name", "form_last_name", "form_enter_last_name", "form_phone_number", "form_secondary_phone_number", "form_whatsapp_number", "form_enter_10digit_whatsapp", "form_email", "form_enter_your_email", "form_enter_your_first", "form_enter_your_middle", "form_enter_your_last", "form_enter_10digit_secondary", "form_email_1", "form_enter_your_10digit", "form_select_train_numbers", "form_zone", "form_zone_1", "form_employee_id", "form_depot", "form_reenter_password", "form_divisions", "form_divisions_1", "form_select_coaches", "form_middle_name", "form_select_train_number", "form_select_stations", "form_select_date", "form_whatsapp_number_1", "form_enter_secondary_phone", "form_mobile_number_1", "form_enter_mobile_number", "form_enter_mobile_number_1", "form_related_train", "form_division", "form_depot_1", "form_charting_day", "form_from_station", "form_to_station", "form_direction_updown", "form_start_time", "form_eg_0900_am", "form_end_time", "form_eg_0500_pm", "form_charting_time", "form_return_gap_days", "form_inout", "form_related_train_number", "form_updown", "form_train_type", "form_search_train_number", "form_coaches_comma_separated", "form_eg_h_gsl", "form_enter_coach_names", "form_select_days", "form_add_stoppage", "form_type_a_station", "form_search", "form_stoppages_in_sequence", "form_type_a_station_1", "form_frequency", "form_enter_new_coach", "form_use_comma_to", "form_add_your_comments", "form_search_by_name", "form_train", "form_complaint_type", "form_status", "form_write_your_issue", "form_issue_status", "form_name", "form_search_by_train", "form_train_selection", "form_journey_start_date", "form_ddmmyyyy", "form_coach", "form_berth", "form_issue_name", "form_subissue_name", "form_search_1", "form_widgetstatustype_by", "form_select_date_time", "form_add_your_feedback", "form_search_train_number_1", "form_train_number_1", "form_enter_otp", "form_resolved_yesno", "form_train_name_1", "form_marks_1_to", "form_remarks_by_passenger", "form_passenger_name_1", "form_pnr_number_1", "form_crn_number_1", "form_coach_no_1", "form_berth_no_1", "form_task_status_1", "form_feedback", "form_amount_in_hand", "form_select_user", "form_enter_email_otp", "form_enter_phone_otp", "form_select_coaches_optional", "btn_deny", "btn_enable", "btn_decline", "btn_accept", "btn_self", "btn_other_ca", "btn_other_ehkobhs", "btn_close", "btn_error_snapshoterror", "btn_no_data_available", "btn_your_current_location", "btn_no_location_data", "btn_no_data_available_1", "btn_add_configuration", "btn_select_charting_day", "btn_edit_configuration", "btn_no_coaches_available", "btn_coach_handover_report", "btn_statustype_by", "btn_traintrainno_traintrainname", "btn_other", "btn_no_complaints_found", "btn_pending", "btn_completed", "btn_upload_json_data", "btn_cancel", "btn_delete", "btn_save_selection", "btn_coach_issue_status", "btn_rake_deficiency_report", "btn_no_feedback_available", "btn_save", "snackbar_please_select_a", "snackbar_data_refreshed_successfully", "snackbar_train_location_saved", "snackbar_error_fetching_images", "snackbar_download_started", "snackbar_pdf_downloaded_successfully", "snackbar_could_not_launch", "snackbar_refresh_failed_e", "snackbar_return_gap_updated", "snackbar_data_not_refreshed", "snackbar_invalid_response_format", "snackbar_please_select_a_1", "snackbar_please_select_images", "snackbar_please_select_at", "snackbar_failed_to_upload", "snackbar_error_updating_issue", "snackbar_no_images_available", "snackbar_failed_to_fetch", "snackbar_error_fetching_trains", "snackbar_complaint_updated", "snackbar_update_failed", "snackbar_complaint_deleted_successfully", "snackbar_failed_to_delete", "snackbar_failed_to_submit", "snackbar_error_e", "snackbar_issues_saved_upload", "snackbar_feedback_deleted_successfully", "snackbar_error_deleting_feedback", "snackbar_job_chart_status", "snackbar_no_image_url", "snackbar_image_downloaded_successfully", "snackbar_failed_to_download", "snackbar_failed_to_download_1", "snackbar_otp_sent_successfully", "snackbar_failed_to_send", "snackbar_please_enter_the", "snackbar_email_saved_successfully", "snackbar_failed_to_verify", "snackbar_jobchart_deleted_successfully", "snackbar_failed_to_delete_1", "snackbar_failed_to_load", "text_train_details", "text_please_enable_location_services", "text_location_permissions_required", "text_location_permission_denied_forever", "text_location_permissions_permanently_denied", "text_select_all", "text_location_permissions_error", "text_refresh_failed", "text_turn_on_location_services", "text_no_location_data_available", "text_edit_train_details", "text_search_train_number", "text_select_train_number", "text_monday", "text_tuesday", "text_wednesday", "text_thursday", "text_friday", "text_saturday", "text_sunday", "text_updating_data", "text_return_gap_updated_successfully", "text_train_details_updated_successfully", "text_restrictions", "text_location", "text_time", "text_enable_media_upload", "text_failed_to_load_zones", "text_failed_to_load_divisions", "text_failed_to_load_depots", "text_failed_to_load_train_details", "text_error_loading_train_data", "text_edit_profile", "text_error_loading_authentication_state", "text_verify_email_button", "text_first_name", "text_middle_name", "text_last_name", "text_employee_number", "text_role", "text_depot", "text_phone_number", "text_whatsapp_number", "text_secondary_phone_number", "text_email", "text_phone_number_must_be_10_digits", "text_phone_number_and_secondary_phone_number_must_be_different", "text_change_email", "text_change_password", "text_change_mobile_number", "text_change_whatsapp_number", "text_update_profile", "text_add_trains", "text_up", "text_down", "text_today", "text_yesterday", "text_failed_to_load_zones_add", "text_failed_to_load_divisions_add", "text_failed_to_load_depots_add", "text_please_enter_train_number", "text_train_number_min_digits", "text_please_select_return_gap", "text_coaches_sequence", "text_please_enter_train_name", "text_please_select_charting_day", "text_please_enter_from_station", "text_please_enter_to_station", "text_please_select_direction", "text_please_enter_start_time", "text_please_enter_end_time", "text_please_enter_charting_time", "text_please_enter_train_type", "text_add_new_user", "text_phone_and_secondary_phone_must_be_different", "text_validation_error", "text_submitting_data_please_wait", "text_please_complete_all_required_fields", "text_form_incomplete", "text_enter_first_name", "text_please_enter_first_name", "text_middle_name_optional", "text_enter_middle_name", "text_enter_last_name", "text_please_enter_last_name", "text_secondary_phone_number_optional", "text_whatsapp_number_same_as_phone", "text_use_same_number_for_whatsapp", "text_enter_10_digit_whatsapp_number", "text_please_enter_whatsapp_number", "text_whatsapp_number_must_be_10_digits", "text_please_enter_only_numbers", "text_request_for_add_user", "text_information_dialog_title", "text_please_complete_fields_in_order", "text_personal_information", "text_contact_details", "text_account_settings", "text_authentication_check_failed", "text_enter_a_valid_mobile_number", "text_user_data_not_found", "text_access_token_not_found", "text_update_user", "text_enter_mobile_number", "text_search_for_a_user_to_update_their_details", "text_user_not_found_please_check_the_mobile_number_and_try_again", "text_error_with_details", "text_please_enter_your_email", "text_please_enter_valid_email", "text_invalid_email_format", "text_please_enter_valid_mobile_number", "text_please_enter_new_mobile_number", "text_do_you_want_to_logout", "text_yes", "text_no", "text_inside_train", "text_need_alarm", "text_deactivate", "text_logout", "text_email_otp_error", "text_phone_otp_error", "text_please_select_a_train_number", "text_add_update", "text_an_error_occurred_while_adding", "text_select_coaches_optional", "text_select_coaches_from_list", "text_please_select_date", "text_need_valid_email_before_deactivation", "text_send_otps_for_verification", "text_failed_to_send_otps", "text_enter_email_otp", "text_enter_phone_otp", "text_please_enter_field", "text_enable_fingerprint_login", "text_deactivate_account", "text_email_verification_optional", "text_verified", "text_email_verification_info", "text_otp_sent_message", "error_berth_number_invalid", "error_form_validation_failed", "error_max_videos", "error_max_videos_short", "error_max_feedback_images", "error_max_feedback_images_short", "error_max_images_reached", "error_picking_media", "error_email_required", "msg_email_verification_initiated", "error_otp_required", "error_something_wrong", "text_daily", "text_yes_lowercase", "text_no_lowercase", "error_berth_number_invalid_passenger", "error_no_train_selected", "error_fetching_charting_time", "dialog_train_not_running_content", "error_validating_pnr", "error_feedback_submission_passenger", "error_max_images_reached_passenger", "error_picking_media_passenger", "error_generic_passenger", "error_something_wrong_passenger", "text_check_inbox", "text_check_spam", "text_add_safe_sender", "msg_otp_sent_check_folders", "msg_error_generic", "msg_something_went_wrong", "status_yes_lowercase", "status_no_lowercase", "passenger_feedback_app_bar_title", "passenger_feedback_main_title", "form_train_name_readonly", "hint_search_train_number", "category_ac", "category_nonac", "category_tt", "category_rm", "rating_excellent", "rating_very_good", "rating_good", "rating_average", "rating_poor", "feedback_table_instruction", "feedback_table_item_header", "dialog_train_not_running", "msg_train_not_running_details", "msg_running_days_daily", "msg_running_days_na", "validation_provide_rating", "msg_rm_fields_required", "msg_upload_complete_wait", "msg_videos_limit", "msg_videos_limit_reached", "feedback_ac_item_1", "feedback_ac_item_2", "feedback_ac_item_3", "feedback_ac_item_4", "feedback_ac_item_5", "feedback_nonac_item_1", "feedback_nonac_item_2", "feedback_nonac_item_3", "button_ok", "error_fetch_train_name", "error_invalid_pnr", "success_pnr_fetched", "error_pnr_validation", "success_feedback_submitted", "error_feedback_submission", "error_unexpected", "email_verification_info_title", "email_verification_spam_notice", "email_verification_after_otp", "email_verification_understand", "success_email_verification", "error_prefix", "success_otp_verified", "upload_pnr_image", "selected_images", "no_pnr_images_selected", "upload_limit_pnr_3", "media_source_camera", "media_source_gallery", "upload_limit_pnr_max", "upload_limit_reached_message", "error_picking_media_simple", "pick_images_videos_feedback", "no_feedback_images_selected", "selected_videos", "select_media_type", "media_type_image", "media_type_video", "upload_limit_videos_3", "upload_limit_videos_max", "upload_limit_feedback_images_3", "upload_limit_feedback_images_max", "text_pnr_document", "text_feedback_media", "loading_deleting_feedback", "success_feedback_deleted", "error_deleting_feedback", "text_train_no", "text_not_verified", "btn_review", "btn_retry", "msg_no_feedback_available", "dialog_title_review_feedback", "text_status_pending_editable", "text_status_completed_not_editable", "text_pnr_documents", "text_failed_to_load_image", "text_tap_to_play_video", "form_remarks_hint", "form_resolved_yes_no", "form_marks_1_to_10", "status_none_selected", "btn_update", "validation_select_issue_type", "validation_select_sub_issue_type", "validation_select_resolved_status", "validation_select_marks", "validation_enter_valid_email", "validation_enter_otp", "validation_remarks_max_100", "msg_cannot_verify_email_completed", "msg_cannot_verify_otp_completed", "msg_otp_verified_successfully", "msg_feedback_updated_successfully", "msg_failed_to_update_feedback", "msg_failed_to_load_video", "msg_error_with_details", "msg_error_or_something_wrong", "loading_updating_feedback", "text_character_count_100", "dialog_email_verification_info_title", "dialog_email_verification_spam_notice", "dialog_after_requesting_otp", "dialog_check_inbox_first", "dialog_check_spam_folder", "dialog_add_safe_sender", "text_otp_sent_check_folders", "form_issue_type_label", "form_sub_issue_type_label", "form_task_status_label", "status_pending_option", "status_completed_option", "text_need_help_reach_out", "error_could_not_launch_phone", "text_notifications", "tooltip_test_notifications", "tooltip_notification_settings", "tooltip_mark_all_as_read", "tooltip_clear_all_notifications", "text_no_notifications", "dialog_title_clear_notifications", "dialog_content_clear_notifications", "btn_clear", "text_notification_fallback", "text_coach_details", "table_header_station", "table_header_coach", "table_header_onboard", "table_header_deboard", "table_header_vacant", "text_notification_system_tests", "text_notification_system_testing", "text_notification_test_description", "text_ready_to_run_notification_tests", "btn_run_all_tests", "btn_running_tests", "btn_individual_tests", "text_test_results", "text_no_test_results_yet", "text_run_tests_to_see_results", "text_running_comprehensive_tests", "text_all_tests_completed_successfully", "text_some_tests_failed", "text_test_execution_failed", "dialog_title_select_test", "test_fcm_token_generation", "test_firestore_token_storage", "test_complete_notification_sync", "test_cloud_function_endpoint", "test_enhanced_notification", "test_debug_firestore_token_storage", "test_force_token_refresh", "test_quick_token_check", "text_running_test", "text_test_completed_successfully", "text_test_failed", "text_no_test_data_available", "text_test_passed", "text_test_failed_label", "rail_sathi_app_bar_title", "tab_write_complaint", "tab_view_complaints", "error_could_not_launch_link", "error_failed_to_load_issues", "error_invalid_response_format", "error_failed_to_load_trip_report", "msg_data_refreshed_successfully", "error_refresh_failed", "error_select_train_date_first", "text_no_coaches_found", "text_please_select_train_date", "text_coach_label", "text_no_issues_reported", "text_reported_issues", "text_unknown_issue", "btn_manage_issues", "text_train_rake_deficiency_report", "text_rake_deficiency_report_issues", "tooltip_upload_coach_image", "btn_upload_image_video", "btn_submit_issue", "form_pnr", "form_search_by_train_number_or_name", "form_dd_mm_yyyy", "btn_next", "tab_cleaning_issues", "tab_linen_related_issues", "msg_please_fill_all_required_details", "text_ehk_not_assigned", "msg_pnr_validation_error_e", "msg_are_you_sure_delete_image", "text_selected_date", "text_date", "text_pnr", "msg_are_you_sure_delete_complaint", "reports_app_bar_title", "btn_attendance_report", "btn_round_rake_deficiency_report", "btn_train_report", "btn_detailed_attendance_report", "btn_detailed_round_trip_attendance_report", "btn_obhs_to_mcc_handover", "btn_mcc_to_obhs_handover", "btn_monthly_attendance_report", "btn_monthly_round_trip_attendance_report", "text_daily_reports", "text_monthly_reports", "btn_show_all", "btn_show_less", "section_daily_reports", "section_monthly_reports", "section_attendance_for", "report_attendance", "report_detailed_attendance", "report_detailed_round_trip", "report_obhs_to_mcc_handover", "report_monthly_attendance", "report_monthly_round_trip", "report_monthly_with_mobile", "text_notification_settings", "text_save_settings", "text_onboarding_notifications", "text_enable_onboarding_notifications", "text_station_approach_alerts", "text_boarding_alerts", "text_off_boarding_alerts", "text_proximity_alerts", "text_timing_settings", "text_advance_notice_minutes", "text_proximity_threshold_km", "text_coach_filters", "text_enable_coach_specific_filtering", "text_enabled_coach_types", "text_sound_vibration", "text_enable_sound", "text_enable_vibration", "text_advanced_settings", "text_background_notifications", "text_location_based_notifications", "text_max_notifications_per_hour", "msg_notification_preferences_saved", "msg_error_saving_preferences", "error_fetching_trains", "form_mobile", "form_enter_train_number", "form_description", "dropdown_other", "dropdown_cleaning", "dropdown_linen", "dropdown_pending", "dropdown_completed", "dialog_delete_image", "dialog_delete_image_confirm", "dialog_delete_complaint", "dialog_delete_complaint_confirm", "btn_add_image", "btn_save_changes", "btn_edit", "btn_select_date", "text_edit_complaint", "form_train_label", "error_failed_to_fetch_complaints", "text_berth_no", "msg_image_deleted", "msg_complaint_updated", "msg_update_failed", "msg_complaint_deleted_success", "msg_failed_to_delete_complaint", "snackbar_image_deleted", "snackbar_failed_to_delete_image", "snackbar_failed_to_delete_complaint", "snackbar_failed_to_fetch_complaints", "btn_rake_deficiency_report_simple", "btn_monthly_with_mobile", "btn_monthly_without_mobile", "text_info", "text_reports_for", "text_trains_not_running", "text_train_not_running", "text_please_select_relevant_trains", "text_following_trains_not_running", "text_train_label", "text_running_days_label", "text_running_trains_on", "text_train_not_running_message", "pnr_status_title", "hint_enter_pnr_number", "label_pnr_number", "btn_check_pnr", "text_no_pnr_data_found", "error_invalid_pnr_number", "error_failed_fetch_pnr", "label_train_number", "label_train_name", "label_boarding_date", "label_from", "label_to", "label_class", "label_departure", "label_arrival", "label_overall_status", "label_booking_date", "label_passenger", "label_coach_berth", "label_ehk_users", "label_ca_users", "label_obhs_users"], "ml": ["text_reenter_otp", "text_deny", "text_enable", "text_location_access_required", "text_decline", "text_accept", "text_confirm_delete", "text_delete", "text_storage_permission_is", "text_please_select_a", "text_update_required", "text_update_now", "text_please_select_a_1", "text_all_details_updated", "text_failed_to_update", "text_data_refreshed_successfully", "text_train_location_saved", "text_self", "text_other_ca", "text_other_ehkobhs", "text_chart_has_not", "text_camera", "text_gallery", "text_getting_location", "text_error_message", "text_failed_to_get", "text_error_fetching_images", "text_back_to_all", "text_submit", "text_upload_status", "text_compressing_image", "text_upload_entrykeysubstring0_6", "text_image_uploading", "text_error_snapshoterror", "text_latitude_entrylatitude", "text_longitude_entrylongitude", "text_distance_entrydistance_km", "text_updated_by_entryupdatedby", "text_no_data_available", "text_show_more", "text_show_less", "text_could_not_open", "text_download_started", "text_pdf_downloaded_successfully", "text_download", "text_get_in_email", "text_could_not_launch", "text_permission_denied", "text_requested_users", "text_end_date_cannot", "text_please_select_both", "text_update_user_details", "text_request_for_update", "text_i_dont_have", "text_information", "text_request_for_sign", "text_forgot_password", "text_forgotten_password", "text_mobile_otp_login", "text_enter_otp", "text_otp_sent_to_mobile", "text_profile", "text_error", "text_sign_in_with", "text_new_user_sign", "text_please_enter_a", "text_failed_to_fetch", "text_check_pnr_status", "text_pnr_number", "text_no_pnr_data", "text_please_turn_on", "text_an_error_occurred", "text_your_current_location", "text_location_services_disabled", "text_please_enable_location", "text_location_permission_denied", "text_open_settings", "text_location_permission_denied_1", "text_refresh_failed_e", "text_no_location_data", "text_no_data_available_1", "text_no_train_details", "text_none", "text_download_pdf_for", "text_mail_pdf_for", "text_add_configuration", "text_select_charting_day", "text_submitting", "text_return_gap_updated", "text_data_not_refreshed", "text_add", "text_train_details_updated", "text_add_train_details", "text_add_train", "text_stoppages_in_sequence", "text_submitting_train_details", "text_edit_configuration", "text_no_coaches_available", "text_invalid_response_format", "text_please_select_a_2", "text_coach_handover_report", "text_save_selection", "text_select_media_type", "text_image", "text_video", "text_please_select_images", "text_please_select_at", "text_failed_to_upload", "text_error_updating_issue", "text_statustype_by", "text_no_images_available", "text_issuesubissue", "text_status", "text_submitupdate", "text_reportedby", "text_fixedby", "text_resolvedby", "text_pick_images", "text_uploading", "text_submit_upload", "text_retry", "text_approve", "text_no_requests_selected", "text_are_you_sure", "text_requeststoprocesslength_users_approved", "text_confirm_denial", "text_deny_request", "text_approve_selected", "text_approve_all", "text_processing_requests", "text_clear_search", "text_failed_to_fetch_1", "text_error_fetching_trains", "text_traintrainno_traintrainname", "text_other", "text_existing_images", "text_delete_image", "text_image_deleted", "text_newly_selected_images", "text_add_image", "text_complaint_updated", "text_update_failed", "text_save_changes", "text_delete_complaint", "text_are_you_sure_1", "text_complaint_deleted_successfully", "text_failed_to_delete", "text_select_date", "text_no_complaints_found", "text_train_no_complainttrainnumber", "text_date_complaintcomplaindate", "text_pnr_complaintpnrnumber", "text_edit", "text_complaint_submitted_successfully", "text_failed_to_submit", "text_error_e", "text_ehk_ehkdisplay", "text_pending", "text_completed", "text_upload_imagevideo", "text_submit_issue", "text_validate", "text_next", "text_error_loading_authentication", "text_storage_permission_required", "text_please_select_a_3", "text_error_json_file", "text_selection_cleared", "text_upload_json_data", "text_update", "text_add_issue", "text_no_subissues", "text_select_issue", "text_add_subissue", "text_add_new_item", "text_images_or_videos", "text_please_select_an", "text_issues_saved_upload", "text_select_issues_for", "text_pick_imagesvideos", "text_please_select_imagevideo", "text_confirm_deletion", "text_delete_report", "text_coach_issue_status", "text_both_person_and", "text_select_widgetstatustype_by", "text_subissue_widgetname", "text_issue_widgetname", "text_confirm", "text_manage_issues", "text_rake_deficiency_report", "text_upload_pnr_image", "text_pick_imagesvideos_for", "text_please_wait_until", "text_submit_feedback", "text_verify_email", "text_check_your_inbox", "text_if_not_found", "text_add_our_domain", "text_i_understand", "text_nonac", "text_select", "text_failed_to_load", "text_review_feedback", "text_deleting_feedback", "text_feedback_deleted_successfully", "text_error_deleting_feedback", "text_no_feedback_available", "text_train_no_trainnumber", "text_non_ac", "text_message", "text_job_chart_status", "text_please_select_all", "text_update_amount_for", "text_assigned", "text_amount", "text_no_image_url", "text_image_downloaded_successfully", "text_failed_to_download", "text_failed_to_download_1", "text_image_detail", "text_download_image", "text_train_trainnumber_details", "text_confirm_deactivation", "text_proceed", "text_add_email", "text_back", "text_email_verification", "text_phone_verification", "text_logout_confirmation", "text_do_you_want", "text_addupdate", "text_otp_sent_successfully", "text_failed_to_send", "text_please_enter_the", "text_failed_to_verify", "text_uploaded_at_formatteddate", "text_uploaded_by_widgetimageresponsecreatedby", "text_id_widgetimageresponseid", "text_coach_widgetimageresponsecoach", "text_issue_widgetimageresponseissue", "text_delete_confirmation", "text_are_you_sure_2", "text_save", "text_ehkca", "text_image_upload_initiated", "text_failed_to_upload_1", "text_please_select_an_1", "text_jobchart_deleted_successfully", "text_failed_to_delete_1", "text_pick_image", "text_upload_image", "text_failed_to_load_1", "text_language", "text_select_language", "text_rail_sathi", "title_location_access_required", "title_upload_status", "title_compressing_image", "title_upload_entrykeysubstring0_6", "title_permission_denied", "title_confirm_delete", "title_add_new_item", "title_add_issue", "title_add_subissue", "title_select_widgetstatustype_by", "title_update_amount_for", "form_train_number", "form_date", "form_select_date_ddmmmyyyy", "form_first_name", "form_enter_first_name", "form_middle_name_optional", "form_enter_middle_name", "form_last_name", "form_enter_last_name", "form_phone_number", "form_secondary_phone_number", "form_whatsapp_number", "form_enter_10digit_whatsapp", "form_email", "form_enter_your_email", "form_enter_your_first", "form_enter_your_middle", "form_enter_your_last", "form_enter_10digit_secondary", "form_email_1", "form_enter_your_10digit", "form_select_train_numbers", "form_zone", "form_zone_1", "form_employee_id", "form_depot", "form_reenter_password", "form_divisions", "form_divisions_1", "form_select_coaches", "form_middle_name", "form_select_train_number", "form_select_stations", "form_select_date", "form_whatsapp_number_1", "form_enter_secondary_phone", "form_mobile_number_1", "form_enter_mobile_number", "form_enter_mobile_number_1", "form_related_train", "form_division", "form_depot_1", "form_charting_day", "form_from_station", "form_to_station", "form_direction_updown", "form_start_time", "form_eg_0900_am", "form_end_time", "form_eg_0500_pm", "form_charting_time", "form_return_gap_days", "form_inout", "form_related_train_number", "form_updown", "form_train_type", "form_search_train_number", "form_coaches_comma_separated", "form_eg_h_gsl", "form_enter_coach_names", "form_select_days", "form_add_stoppage", "form_type_a_station", "form_search", "form_stoppages_in_sequence", "form_type_a_station_1", "form_frequency", "form_enter_new_coach", "form_use_comma_to", "form_add_your_comments", "form_search_by_name", "form_train", "form_complaint_type", "form_status", "form_write_your_issue", "form_issue_status", "form_name", "form_search_by_train", "form_train_selection", "form_journey_start_date", "form_ddmmyyyy", "form_coach", "form_berth", "form_issue_name", "form_subissue_name", "form_search_1", "form_widgetstatustype_by", "form_select_date_time", "form_add_your_feedback", "form_search_train_number_1", "form_train_number_1", "form_enter_otp", "form_resolved_yesno", "form_train_name_1", "form_marks_1_to", "form_remarks_by_passenger", "form_passenger_name_1", "form_pnr_number_1", "form_crn_number_1", "form_coach_no_1", "form_berth_no_1", "form_task_status_1", "form_feedback", "form_amount_in_hand", "form_select_user", "form_enter_email_otp", "form_enter_phone_otp", "form_select_coaches_optional", "btn_deny", "btn_enable", "btn_decline", "btn_accept", "btn_self", "btn_other_ca", "btn_other_ehkobhs", "btn_close", "btn_error_snapshoterror", "btn_no_data_available", "btn_your_current_location", "btn_no_location_data", "btn_no_data_available_1", "btn_add_configuration", "btn_select_charting_day", "btn_edit_configuration", "btn_no_coaches_available", "btn_coach_handover_report", "btn_statustype_by", "btn_traintrainno_traintrainname", "btn_other", "btn_no_complaints_found", "btn_pending", "btn_completed", "btn_upload_json_data", "btn_cancel", "btn_delete", "btn_save_selection", "btn_coach_issue_status", "btn_rake_deficiency_report", "btn_no_feedback_available", "btn_save", "snackbar_please_select_a", "snackbar_data_refreshed_successfully", "snackbar_train_location_saved", "snackbar_error_fetching_images", "snackbar_download_started", "snackbar_pdf_downloaded_successfully", "snackbar_could_not_launch", "snackbar_refresh_failed_e", "snackbar_return_gap_updated", "snackbar_data_not_refreshed", "snackbar_invalid_response_format", "snackbar_please_select_a_1", "snackbar_please_select_images", "snackbar_please_select_at", "snackbar_failed_to_upload", "snackbar_error_updating_issue", "snackbar_no_images_available", "snackbar_failed_to_fetch", "snackbar_error_fetching_trains", "snackbar_complaint_updated", "snackbar_update_failed", "snackbar_complaint_deleted_successfully", "snackbar_failed_to_delete", "snackbar_failed_to_submit", "snackbar_error_e", "snackbar_issues_saved_upload", "snackbar_feedback_deleted_successfully", "snackbar_error_deleting_feedback", "snackbar_job_chart_status", "snackbar_no_image_url", "snackbar_image_downloaded_successfully", "snackbar_failed_to_download", "snackbar_failed_to_download_1", "snackbar_otp_sent_successfully", "snackbar_failed_to_send", "snackbar_please_enter_the", "snackbar_email_saved_successfully", "snackbar_failed_to_verify", "snackbar_jobchart_deleted_successfully", "snackbar_failed_to_delete_1", "snackbar_failed_to_load", "text_train_details", "text_please_enable_location_services", "text_location_permissions_required", "text_location_permission_denied_forever", "text_location_permissions_permanently_denied", "text_select_all", "text_location_permissions_error", "text_refresh_failed", "text_turn_on_location_services", "text_no_location_data_available", "text_edit_train_details", "text_search_train_number", "text_select_train_number", "text_monday", "text_tuesday", "text_wednesday", "text_thursday", "text_friday", "text_saturday", "text_sunday", "text_updating_data", "text_return_gap_updated_successfully", "text_train_details_updated_successfully", "text_restrictions", "text_location", "text_time", "text_enable_media_upload", "text_failed_to_load_zones", "text_failed_to_load_divisions", "text_failed_to_load_depots", "text_failed_to_load_train_details", "text_error_loading_train_data", "text_edit_profile", "text_error_loading_authentication_state", "text_verify_email_button", "text_first_name", "text_middle_name", "text_last_name", "text_employee_number", "text_role", "text_depot", "text_phone_number", "text_whatsapp_number", "text_secondary_phone_number", "text_email", "text_phone_number_must_be_10_digits", "text_phone_number_and_secondary_phone_number_must_be_different", "text_change_email", "text_change_password", "text_change_mobile_number", "text_change_whatsapp_number", "text_update_profile", "text_add_trains", "text_up", "text_down", "text_today", "text_yesterday", "text_failed_to_load_zones_add", "text_failed_to_load_divisions_add", "text_failed_to_load_depots_add", "text_please_enter_train_number", "text_train_number_min_digits", "text_please_select_return_gap", "text_coaches_sequence", "text_please_enter_train_name", "text_please_select_charting_day", "text_please_enter_from_station", "text_please_enter_to_station", "text_please_select_direction", "text_please_enter_start_time", "text_please_enter_end_time", "text_please_enter_charting_time", "text_please_enter_train_type", "text_add_new_user", "text_phone_and_secondary_phone_must_be_different", "text_validation_error", "text_submitting_data_please_wait", "text_please_complete_all_required_fields", "text_form_incomplete", "text_enter_first_name", "text_please_enter_first_name", "text_middle_name_optional", "text_enter_middle_name", "text_enter_last_name", "text_please_enter_last_name", "text_secondary_phone_number_optional", "text_whatsapp_number_same_as_phone", "text_use_same_number_for_whatsapp", "text_enter_10_digit_whatsapp_number", "text_please_enter_whatsapp_number", "text_whatsapp_number_must_be_10_digits", "text_please_enter_only_numbers", "text_request_for_add_user", "text_information_dialog_title", "text_please_complete_fields_in_order", "text_personal_information", "text_contact_details", "text_account_settings", "text_authentication_check_failed", "text_enter_a_valid_mobile_number", "text_user_data_not_found", "text_access_token_not_found", "text_update_user", "text_enter_mobile_number", "text_search_for_a_user_to_update_their_details", "text_user_not_found_please_check_the_mobile_number_and_try_again", "text_error_with_details", "text_please_enter_your_email", "text_please_enter_valid_email", "text_invalid_email_format", "text_please_enter_valid_mobile_number", "text_please_enter_new_mobile_number", "text_do_you_want_to_logout", "text_yes", "text_no", "text_inside_train", "text_need_alarm", "text_deactivate", "text_logout", "text_email_otp_error", "text_phone_otp_error", "text_please_select_a_train_number", "text_add_update", "text_an_error_occurred_while_adding", "text_select_coaches_optional", "text_select_coaches_from_list", "text_please_select_date", "text_need_valid_email_before_deactivation", "text_send_otps_for_verification", "text_failed_to_send_otps", "text_enter_email_otp", "text_enter_phone_otp", "text_please_enter_field", "text_enable_fingerprint_login", "text_deactivate_account", "text_email_verification_optional", "text_verified", "text_email_verification_info", "text_otp_sent_message", "error_berth_number_invalid", "error_form_validation_failed", "error_max_videos", "error_max_videos_short", "error_max_feedback_images", "error_max_feedback_images_short", "error_max_images_reached", "error_picking_media", "error_email_required", "msg_email_verification_initiated", "error_otp_required", "error_something_wrong", "text_daily", "text_yes_lowercase", "text_no_lowercase", "error_berth_number_invalid_passenger", "error_no_train_selected", "error_fetching_charting_time", "dialog_train_not_running_content", "error_validating_pnr", "error_feedback_submission_passenger", "error_max_images_reached_passenger", "error_picking_media_passenger", "error_generic_passenger", "error_something_wrong_passenger", "text_check_inbox", "text_check_spam", "text_add_safe_sender", "msg_otp_sent_check_folders", "msg_error_generic", "msg_something_went_wrong", "status_yes_lowercase", "status_no_lowercase", "passenger_feedback_app_bar_title", "passenger_feedback_main_title", "form_train_name_readonly", "hint_search_train_number", "category_ac", "category_nonac", "category_tt", "category_rm", "rating_excellent", "rating_very_good", "rating_good", "rating_average", "rating_poor", "feedback_table_instruction", "feedback_table_item_header", "dialog_train_not_running", "msg_train_not_running_details", "msg_running_days_daily", "msg_running_days_na", "validation_provide_rating", "msg_rm_fields_required", "msg_upload_complete_wait", "msg_videos_limit", "msg_videos_limit_reached", "feedback_ac_item_1", "feedback_ac_item_2", "feedback_ac_item_3", "feedback_ac_item_4", "feedback_ac_item_5", "feedback_nonac_item_1", "feedback_nonac_item_2", "feedback_nonac_item_3", "button_ok", "error_fetch_train_name", "error_invalid_pnr", "success_pnr_fetched", "error_pnr_validation", "success_feedback_submitted", "error_feedback_submission", "error_unexpected", "email_verification_info_title", "email_verification_spam_notice", "email_verification_after_otp", "email_verification_understand", "success_email_verification", "error_prefix", "success_otp_verified", "upload_pnr_image", "selected_images", "no_pnr_images_selected", "upload_limit_pnr_3", "media_source_camera", "media_source_gallery", "upload_limit_pnr_max", "upload_limit_reached_message", "error_picking_media_simple", "pick_images_videos_feedback", "no_feedback_images_selected", "selected_videos", "select_media_type", "media_type_image", "media_type_video", "upload_limit_videos_3", "upload_limit_videos_max", "upload_limit_feedback_images_3", "upload_limit_feedback_images_max", "text_pnr_document", "text_feedback_media", "loading_deleting_feedback", "success_feedback_deleted", "error_deleting_feedback", "text_train_no", "text_not_verified", "btn_review", "btn_retry", "msg_no_feedback_available", "dialog_title_review_feedback", "text_status_pending_editable", "text_status_completed_not_editable", "text_pnr_documents", "text_failed_to_load_image", "text_tap_to_play_video", "form_remarks_hint", "form_resolved_yes_no", "form_marks_1_to_10", "status_none_selected", "btn_update", "validation_select_issue_type", "validation_select_sub_issue_type", "validation_select_resolved_status", "validation_select_marks", "validation_enter_valid_email", "validation_enter_otp", "validation_remarks_max_100", "msg_cannot_verify_email_completed", "msg_cannot_verify_otp_completed", "msg_otp_verified_successfully", "msg_feedback_updated_successfully", "msg_failed_to_update_feedback", "msg_failed_to_load_video", "msg_error_with_details", "msg_error_or_something_wrong", "loading_updating_feedback", "text_character_count_100", "dialog_email_verification_info_title", "dialog_email_verification_spam_notice", "dialog_after_requesting_otp", "dialog_check_inbox_first", "dialog_check_spam_folder", "dialog_add_safe_sender", "text_otp_sent_check_folders", "form_issue_type_label", "form_sub_issue_type_label", "form_task_status_label", "status_pending_option", "status_completed_option", "text_need_help_reach_out", "error_could_not_launch_phone", "text_notifications", "tooltip_test_notifications", "tooltip_notification_settings", "tooltip_mark_all_as_read", "tooltip_clear_all_notifications", "text_no_notifications", "dialog_title_clear_notifications", "dialog_content_clear_notifications", "btn_clear", "text_notification_fallback", "text_coach_details", "table_header_station", "table_header_coach", "table_header_onboard", "table_header_deboard", "table_header_vacant", "text_notification_system_tests", "text_notification_system_testing", "text_notification_test_description", "text_ready_to_run_notification_tests", "btn_run_all_tests", "btn_running_tests", "btn_individual_tests", "text_test_results", "text_no_test_results_yet", "text_run_tests_to_see_results", "text_running_comprehensive_tests", "text_all_tests_completed_successfully", "text_some_tests_failed", "text_test_execution_failed", "dialog_title_select_test", "test_fcm_token_generation", "test_firestore_token_storage", "test_complete_notification_sync", "test_cloud_function_endpoint", "test_enhanced_notification", "test_debug_firestore_token_storage", "test_force_token_refresh", "test_quick_token_check", "text_running_test", "text_test_completed_successfully", "text_test_failed", "text_no_test_data_available", "text_test_passed", "text_test_failed_label", "rail_sathi_app_bar_title", "tab_write_complaint", "tab_view_complaints", "error_could_not_launch_link", "error_failed_to_load_issues", "error_invalid_response_format", "error_failed_to_load_trip_report", "msg_data_refreshed_successfully", "error_refresh_failed", "error_select_train_date_first", "text_no_coaches_found", "text_please_select_train_date", "text_coach_label", "text_no_issues_reported", "text_reported_issues", "text_unknown_issue", "btn_manage_issues", "text_train_rake_deficiency_report", "text_rake_deficiency_report_issues", "tooltip_upload_coach_image", "btn_upload_image_video", "btn_submit_issue", "form_pnr", "form_search_by_train_number_or_name", "form_dd_mm_yyyy", "btn_next", "tab_cleaning_issues", "tab_linen_related_issues", "msg_please_fill_all_required_details", "text_ehk_not_assigned", "msg_pnr_validation_error_e", "msg_are_you_sure_delete_image", "text_selected_date", "text_date", "text_pnr", "msg_are_you_sure_delete_complaint", "reports_app_bar_title", "btn_attendance_report", "btn_round_rake_deficiency_report", "btn_train_report", "btn_detailed_attendance_report", "btn_detailed_round_trip_attendance_report", "btn_obhs_to_mcc_handover", "btn_mcc_to_obhs_handover", "btn_monthly_attendance_report", "btn_monthly_round_trip_attendance_report", "text_daily_reports", "text_monthly_reports", "btn_show_all", "btn_show_less", "section_daily_reports", "section_monthly_reports", "section_attendance_for", "report_attendance", "report_detailed_attendance", "report_detailed_round_trip", "report_obhs_to_mcc_handover", "report_monthly_attendance", "report_monthly_round_trip", "report_monthly_with_mobile", "text_notification_settings", "text_save_settings", "text_onboarding_notifications", "text_enable_onboarding_notifications", "text_station_approach_alerts", "text_boarding_alerts", "text_off_boarding_alerts", "text_proximity_alerts", "text_timing_settings", "text_advance_notice_minutes", "text_proximity_threshold_km", "text_coach_filters", "text_enable_coach_specific_filtering", "text_enabled_coach_types", "text_sound_vibration", "text_enable_sound", "text_enable_vibration", "text_advanced_settings", "text_background_notifications", "text_location_based_notifications", "text_max_notifications_per_hour", "msg_notification_preferences_saved", "msg_error_saving_preferences", "error_fetching_trains", "form_mobile", "form_enter_train_number", "form_description", "dropdown_other", "dropdown_cleaning", "dropdown_linen", "dropdown_pending", "dropdown_completed", "dialog_delete_image", "dialog_delete_image_confirm", "dialog_delete_complaint", "dialog_delete_complaint_confirm", "btn_add_image", "btn_save_changes", "btn_edit", "btn_select_date", "text_edit_complaint", "form_train_label", "error_failed_to_fetch_complaints", "text_berth_no", "msg_image_deleted", "msg_complaint_updated", "msg_update_failed", "msg_complaint_deleted_success", "msg_failed_to_delete_complaint", "snackbar_image_deleted", "snackbar_failed_to_delete_image", "snackbar_failed_to_delete_complaint", "snackbar_failed_to_fetch_complaints", "btn_rake_deficiency_report_simple", "btn_monthly_with_mobile", "btn_monthly_without_mobile", "text_info", "text_reports_for", "text_trains_not_running", "text_train_not_running", "text_please_select_relevant_trains", "text_following_trains_not_running", "text_train_label", "text_running_days_label", "text_running_trains_on", "text_train_not_running_message", "pnr_status_title", "hint_enter_pnr_number", "label_pnr_number", "btn_check_pnr", "text_no_pnr_data_found", "error_invalid_pnr_number", "error_failed_fetch_pnr", "label_train_number", "label_train_name", "label_boarding_date", "label_from", "label_to", "label_class", "label_departure", "label_arrival", "label_overall_status", "label_booking_date", "label_passenger", "label_coach_berth", "label_ehk_users", "label_ca_users", "label_obhs_users"], "mr": ["text_reenter_otp", "text_deny", "text_enable", "text_location_access_required", "text_decline", "text_accept", "text_confirm_delete", "text_delete", "text_storage_permission_is", "text_please_select_a", "text_update_required", "text_update_now", "text_please_select_a_1", "text_all_details_updated", "text_failed_to_update", "text_data_refreshed_successfully", "text_train_location_saved", "text_self", "text_other_ca", "text_other_ehkobhs", "text_chart_has_not", "text_camera", "text_gallery", "text_getting_location", "text_error_message", "text_failed_to_get", "text_error_fetching_images", "text_back_to_all", "text_submit", "text_upload_status", "text_compressing_image", "text_upload_entrykeysubstring0_6", "text_image_uploading", "text_error_snapshoterror", "text_latitude_entrylatitude", "text_longitude_entrylongitude", "text_distance_entrydistance_km", "text_updated_by_entryupdatedby", "text_no_data_available", "text_show_more", "text_show_less", "text_could_not_open", "text_download_started", "text_pdf_downloaded_successfully", "text_download", "text_get_in_email", "text_could_not_launch", "text_permission_denied", "text_requested_users", "text_end_date_cannot", "text_please_select_both", "text_update_user_details", "text_request_for_update", "text_i_dont_have", "text_information", "text_request_for_sign", "text_forgot_password", "text_forgotten_password", "text_mobile_otp_login", "text_enter_otp", "text_otp_sent_to_mobile", "text_profile", "text_error", "text_sign_in_with", "text_new_user_sign", "text_please_enter_a", "text_failed_to_fetch", "text_check_pnr_status", "text_pnr_number", "text_no_pnr_data", "text_please_turn_on", "text_an_error_occurred", "text_your_current_location", "text_location_services_disabled", "text_please_enable_location", "text_location_permission_denied", "text_open_settings", "text_location_permission_denied_1", "text_refresh_failed_e", "text_no_location_data", "text_no_data_available_1", "text_no_train_details", "text_none", "text_download_pdf_for", "text_mail_pdf_for", "text_add_configuration", "text_select_charting_day", "text_submitting", "text_return_gap_updated", "text_data_not_refreshed", "text_add", "text_train_details_updated", "text_add_train_details", "text_add_train", "text_stoppages_in_sequence", "text_submitting_train_details", "text_edit_configuration", "text_no_coaches_available", "text_invalid_response_format", "text_please_select_a_2", "text_coach_handover_report", "text_save_selection", "text_select_media_type", "text_image", "text_video", "text_please_select_images", "text_please_select_at", "text_failed_to_upload", "text_error_updating_issue", "text_statustype_by", "text_no_images_available", "text_issuesubissue", "text_status", "text_submitupdate", "text_reportedby", "text_fixedby", "text_resolvedby", "text_pick_images", "text_uploading", "text_submit_upload", "text_retry", "text_approve", "text_no_requests_selected", "text_are_you_sure", "text_requeststoprocesslength_users_approved", "text_confirm_denial", "text_deny_request", "text_approve_selected", "text_approve_all", "text_processing_requests", "text_clear_search", "text_failed_to_fetch_1", "text_error_fetching_trains", "text_traintrainno_traintrainname", "text_other", "text_existing_images", "text_delete_image", "text_image_deleted", "text_newly_selected_images", "text_add_image", "text_complaint_updated", "text_update_failed", "text_save_changes", "text_delete_complaint", "text_are_you_sure_1", "text_complaint_deleted_successfully", "text_failed_to_delete", "text_select_date", "text_no_complaints_found", "text_train_no_complainttrainnumber", "text_date_complaintcomplaindate", "text_pnr_complaintpnrnumber", "text_edit", "text_complaint_submitted_successfully", "text_failed_to_submit", "text_error_e", "text_ehk_ehkdisplay", "text_pending", "text_completed", "text_upload_imagevideo", "text_submit_issue", "text_validate", "text_next", "text_error_loading_authentication", "text_storage_permission_required", "text_please_select_a_3", "text_error_json_file", "text_selection_cleared", "text_upload_json_data", "text_update", "text_add_issue", "text_no_subissues", "text_select_issue", "text_add_subissue", "text_add_new_item", "text_images_or_videos", "text_please_select_an", "text_issues_saved_upload", "text_select_issues_for", "text_pick_imagesvideos", "text_please_select_imagevideo", "text_confirm_deletion", "text_delete_report", "text_coach_issue_status", "text_both_person_and", "text_select_widgetstatustype_by", "text_subissue_widgetname", "text_issue_widgetname", "text_confirm", "text_manage_issues", "text_rake_deficiency_report", "text_upload_pnr_image", "text_pick_imagesvideos_for", "text_please_wait_until", "text_submit_feedback", "text_verify_email", "text_check_your_inbox", "text_if_not_found", "text_add_our_domain", "text_i_understand", "text_nonac", "text_select", "text_failed_to_load", "text_review_feedback", "text_deleting_feedback", "text_feedback_deleted_successfully", "text_error_deleting_feedback", "text_no_feedback_available", "text_train_no_trainnumber", "text_non_ac", "text_message", "text_job_chart_status", "text_please_select_all", "text_update_amount_for", "text_assigned", "text_amount", "text_no_image_url", "text_image_downloaded_successfully", "text_failed_to_download", "text_failed_to_download_1", "text_image_detail", "text_download_image", "text_train_trainnumber_details", "text_confirm_deactivation", "text_proceed", "text_add_email", "text_back", "text_email_verification", "text_phone_verification", "text_logout_confirmation", "text_do_you_want", "text_addupdate", "text_otp_sent_successfully", "text_failed_to_send", "text_please_enter_the", "text_failed_to_verify", "text_uploaded_at_formatteddate", "text_uploaded_by_widgetimageresponsecreatedby", "text_id_widgetimageresponseid", "text_coach_widgetimageresponsecoach", "text_issue_widgetimageresponseissue", "text_delete_confirmation", "text_are_you_sure_2", "text_save", "text_ehkca", "text_image_upload_initiated", "text_failed_to_upload_1", "text_please_select_an_1", "text_jobchart_deleted_successfully", "text_failed_to_delete_1", "text_pick_image", "text_upload_image", "text_failed_to_load_1", "text_language", "text_select_language", "text_rail_sathi", "title_location_access_required", "title_upload_status", "title_compressing_image", "title_upload_entrykeysubstring0_6", "title_permission_denied", "title_confirm_delete", "title_add_new_item", "title_add_issue", "title_add_subissue", "title_select_widgetstatustype_by", "title_update_amount_for", "form_train_number", "form_date", "form_select_date_ddmmmyyyy", "form_first_name", "form_enter_first_name", "form_middle_name_optional", "form_enter_middle_name", "form_last_name", "form_enter_last_name", "form_phone_number", "form_secondary_phone_number", "form_whatsapp_number", "form_enter_10digit_whatsapp", "form_email", "form_enter_your_email", "form_enter_your_first", "form_enter_your_middle", "form_enter_your_last", "form_enter_10digit_secondary", "form_email_1", "form_enter_your_10digit", "form_select_train_numbers", "form_zone", "form_zone_1", "form_employee_id", "form_depot", "form_reenter_password", "form_divisions", "form_divisions_1", "form_select_coaches", "form_middle_name", "form_select_train_number", "form_select_stations", "form_select_date", "form_whatsapp_number_1", "form_enter_secondary_phone", "form_mobile_number_1", "form_enter_mobile_number", "form_enter_mobile_number_1", "form_related_train", "form_division", "form_depot_1", "form_charting_day", "form_from_station", "form_to_station", "form_direction_updown", "form_start_time", "form_eg_0900_am", "form_end_time", "form_eg_0500_pm", "form_charting_time", "form_return_gap_days", "form_inout", "form_related_train_number", "form_updown", "form_train_type", "form_search_train_number", "form_coaches_comma_separated", "form_eg_h_gsl", "form_enter_coach_names", "form_select_days", "form_add_stoppage", "form_type_a_station", "form_search", "form_stoppages_in_sequence", "form_type_a_station_1", "form_frequency", "form_enter_new_coach", "form_use_comma_to", "form_add_your_comments", "form_search_by_name", "form_train", "form_complaint_type", "form_status", "form_write_your_issue", "form_issue_status", "form_name", "form_search_by_train", "form_train_selection", "form_journey_start_date", "form_ddmmyyyy", "form_coach", "form_berth", "form_issue_name", "form_subissue_name", "form_search_1", "form_widgetstatustype_by", "form_select_date_time", "form_add_your_feedback", "form_search_train_number_1", "form_train_number_1", "form_enter_otp", "form_resolved_yesno", "form_train_name_1", "form_marks_1_to", "form_remarks_by_passenger", "form_passenger_name_1", "form_pnr_number_1", "form_crn_number_1", "form_coach_no_1", "form_berth_no_1", "form_task_status_1", "form_feedback", "form_amount_in_hand", "form_select_user", "form_enter_email_otp", "form_enter_phone_otp", "form_select_coaches_optional", "btn_deny", "btn_enable", "btn_decline", "btn_accept", "btn_self", "btn_other_ca", "btn_other_ehkobhs", "btn_close", "btn_error_snapshoterror", "btn_no_data_available", "btn_your_current_location", "btn_no_location_data", "btn_no_data_available_1", "btn_add_configuration", "btn_select_charting_day", "btn_edit_configuration", "btn_no_coaches_available", "btn_coach_handover_report", "btn_statustype_by", "btn_traintrainno_traintrainname", "btn_other", "btn_no_complaints_found", "btn_pending", "btn_completed", "btn_upload_json_data", "btn_cancel", "btn_delete", "btn_save_selection", "btn_coach_issue_status", "btn_rake_deficiency_report", "btn_no_feedback_available", "btn_save", "snackbar_please_select_a", "snackbar_data_refreshed_successfully", "snackbar_train_location_saved", "snackbar_error_fetching_images", "snackbar_download_started", "snackbar_pdf_downloaded_successfully", "snackbar_could_not_launch", "snackbar_refresh_failed_e", "snackbar_return_gap_updated", "snackbar_data_not_refreshed", "snackbar_invalid_response_format", "snackbar_please_select_a_1", "snackbar_please_select_images", "snackbar_please_select_at", "snackbar_failed_to_upload", "snackbar_error_updating_issue", "snackbar_no_images_available", "snackbar_failed_to_fetch", "snackbar_error_fetching_trains", "snackbar_complaint_updated", "snackbar_update_failed", "snackbar_complaint_deleted_successfully", "snackbar_failed_to_delete", "snackbar_failed_to_submit", "snackbar_error_e", "snackbar_issues_saved_upload", "snackbar_feedback_deleted_successfully", "snackbar_error_deleting_feedback", "snackbar_job_chart_status", "snackbar_no_image_url", "snackbar_image_downloaded_successfully", "snackbar_failed_to_download", "snackbar_failed_to_download_1", "snackbar_otp_sent_successfully", "snackbar_failed_to_send", "snackbar_please_enter_the", "snackbar_email_saved_successfully", "snackbar_failed_to_verify", "snackbar_jobchart_deleted_successfully", "snackbar_failed_to_delete_1", "snackbar_failed_to_load", "text_train_details", "text_please_enable_location_services", "text_location_permissions_required", "text_location_permission_denied_forever", "text_location_permissions_permanently_denied", "text_select_all", "text_location_permissions_error", "text_refresh_failed", "text_turn_on_location_services", "text_no_location_data_available", "text_edit_train_details", "text_search_train_number", "text_select_train_number", "text_monday", "text_tuesday", "text_wednesday", "text_thursday", "text_friday", "text_saturday", "text_sunday", "text_updating_data", "text_return_gap_updated_successfully", "text_train_details_updated_successfully", "text_restrictions", "text_location", "text_time", "text_enable_media_upload", "text_failed_to_load_zones", "text_failed_to_load_divisions", "text_failed_to_load_depots", "text_failed_to_load_train_details", "text_error_loading_train_data", "text_edit_profile", "text_error_loading_authentication_state", "text_verify_email_button", "text_first_name", "text_middle_name", "text_last_name", "text_employee_number", "text_role", "text_depot", "text_phone_number", "text_whatsapp_number", "text_secondary_phone_number", "text_email", "text_phone_number_must_be_10_digits", "text_phone_number_and_secondary_phone_number_must_be_different", "text_change_email", "text_change_password", "text_change_mobile_number", "text_change_whatsapp_number", "text_update_profile", "text_add_trains", "text_up", "text_down", "text_today", "text_yesterday", "text_failed_to_load_zones_add", "text_failed_to_load_divisions_add", "text_failed_to_load_depots_add", "text_please_enter_train_number", "text_train_number_min_digits", "text_please_select_return_gap", "text_coaches_sequence", "text_please_enter_train_name", "text_please_select_charting_day", "text_please_enter_from_station", "text_please_enter_to_station", "text_please_select_direction", "text_please_enter_start_time", "text_please_enter_end_time", "text_please_enter_charting_time", "text_please_enter_train_type", "text_add_new_user", "text_phone_and_secondary_phone_must_be_different", "text_validation_error", "text_submitting_data_please_wait", "text_please_complete_all_required_fields", "text_form_incomplete", "text_enter_first_name", "text_please_enter_first_name", "text_middle_name_optional", "text_enter_middle_name", "text_enter_last_name", "text_please_enter_last_name", "text_secondary_phone_number_optional", "text_whatsapp_number_same_as_phone", "text_use_same_number_for_whatsapp", "text_enter_10_digit_whatsapp_number", "text_please_enter_whatsapp_number", "text_whatsapp_number_must_be_10_digits", "text_please_enter_only_numbers", "text_request_for_add_user", "text_information_dialog_title", "text_please_complete_fields_in_order", "text_personal_information", "text_contact_details", "text_account_settings", "text_authentication_check_failed", "text_enter_a_valid_mobile_number", "text_user_data_not_found", "text_access_token_not_found", "text_update_user", "text_enter_mobile_number", "text_search_for_a_user_to_update_their_details", "text_user_not_found_please_check_the_mobile_number_and_try_again", "text_error_with_details", "text_please_enter_your_email", "text_please_enter_valid_email", "text_invalid_email_format", "text_please_enter_valid_mobile_number", "text_please_enter_new_mobile_number", "text_do_you_want_to_logout", "text_yes", "text_no", "text_inside_train", "text_need_alarm", "text_deactivate", "text_logout", "text_email_otp_error", "text_phone_otp_error", "text_please_select_a_train_number", "text_add_update", "text_an_error_occurred_while_adding", "text_select_coaches_optional", "text_select_coaches_from_list", "text_please_select_date", "text_need_valid_email_before_deactivation", "text_send_otps_for_verification", "text_failed_to_send_otps", "text_enter_email_otp", "text_enter_phone_otp", "text_please_enter_field", "text_enable_fingerprint_login", "text_deactivate_account", "text_email_verification_optional", "text_verified", "text_email_verification_info", "text_otp_sent_message", "error_berth_number_invalid", "error_form_validation_failed", "error_max_videos", "error_max_videos_short", "error_max_feedback_images", "error_max_feedback_images_short", "error_max_images_reached", "error_picking_media", "error_email_required", "msg_email_verification_initiated", "error_otp_required", "error_something_wrong", "text_daily", "text_yes_lowercase", "text_no_lowercase", "error_berth_number_invalid_passenger", "error_no_train_selected", "error_fetching_charting_time", "dialog_train_not_running_content", "error_validating_pnr", "error_feedback_submission_passenger", "error_max_images_reached_passenger", "error_picking_media_passenger", "error_generic_passenger", "error_something_wrong_passenger", "text_check_inbox", "text_check_spam", "text_add_safe_sender", "msg_otp_sent_check_folders", "msg_error_generic", "msg_something_went_wrong", "status_yes_lowercase", "status_no_lowercase", "passenger_feedback_app_bar_title", "passenger_feedback_main_title", "form_train_name_readonly", "hint_search_train_number", "category_ac", "category_nonac", "category_tt", "category_rm", "rating_excellent", "rating_very_good", "rating_good", "rating_average", "rating_poor", "feedback_table_instruction", "feedback_table_item_header", "dialog_train_not_running", "msg_train_not_running_details", "msg_running_days_daily", "msg_running_days_na", "validation_provide_rating", "msg_rm_fields_required", "msg_upload_complete_wait", "msg_videos_limit", "msg_videos_limit_reached", "feedback_ac_item_1", "feedback_ac_item_2", "feedback_ac_item_3", "feedback_ac_item_4", "feedback_ac_item_5", "feedback_nonac_item_1", "feedback_nonac_item_2", "feedback_nonac_item_3", "button_ok", "error_fetch_train_name", "error_invalid_pnr", "success_pnr_fetched", "error_pnr_validation", "success_feedback_submitted", "error_feedback_submission", "error_unexpected", "email_verification_info_title", "email_verification_spam_notice", "email_verification_after_otp", "email_verification_understand", "success_email_verification", "error_prefix", "success_otp_verified", "upload_pnr_image", "selected_images", "no_pnr_images_selected", "upload_limit_pnr_3", "media_source_camera", "media_source_gallery", "upload_limit_pnr_max", "upload_limit_reached_message", "error_picking_media_simple", "pick_images_videos_feedback", "no_feedback_images_selected", "selected_videos", "select_media_type", "media_type_image", "media_type_video", "upload_limit_videos_3", "upload_limit_videos_max", "upload_limit_feedback_images_3", "upload_limit_feedback_images_max", "text_pnr_document", "text_feedback_media", "loading_deleting_feedback", "success_feedback_deleted", "error_deleting_feedback", "text_train_no", "text_not_verified", "btn_review", "btn_retry", "msg_no_feedback_available", "dialog_title_review_feedback", "text_status_pending_editable", "text_status_completed_not_editable", "text_pnr_documents", "text_failed_to_load_image", "text_tap_to_play_video", "form_remarks_hint", "form_resolved_yes_no", "form_marks_1_to_10", "status_none_selected", "btn_update", "validation_select_issue_type", "validation_select_sub_issue_type", "validation_select_resolved_status", "validation_select_marks", "validation_enter_valid_email", "validation_enter_otp", "validation_remarks_max_100", "msg_cannot_verify_email_completed", "msg_cannot_verify_otp_completed", "msg_otp_verified_successfully", "msg_feedback_updated_successfully", "msg_failed_to_update_feedback", "msg_failed_to_load_video", "msg_error_with_details", "msg_error_or_something_wrong", "loading_updating_feedback", "text_character_count_100", "dialog_email_verification_info_title", "dialog_email_verification_spam_notice", "dialog_after_requesting_otp", "dialog_check_inbox_first", "dialog_check_spam_folder", "dialog_add_safe_sender", "text_otp_sent_check_folders", "form_issue_type_label", "form_sub_issue_type_label", "form_task_status_label", "status_pending_option", "status_completed_option", "text_need_help_reach_out", "error_could_not_launch_phone", "text_notifications", "tooltip_test_notifications", "tooltip_notification_settings", "tooltip_mark_all_as_read", "tooltip_clear_all_notifications", "text_no_notifications", "dialog_title_clear_notifications", "dialog_content_clear_notifications", "btn_clear", "text_notification_fallback", "text_coach_details", "table_header_station", "table_header_coach", "table_header_onboard", "table_header_deboard", "table_header_vacant", "text_notification_system_tests", "text_notification_system_testing", "text_notification_test_description", "text_ready_to_run_notification_tests", "btn_run_all_tests", "btn_running_tests", "btn_individual_tests", "text_test_results", "text_no_test_results_yet", "text_run_tests_to_see_results", "text_running_comprehensive_tests", "text_all_tests_completed_successfully", "text_some_tests_failed", "text_test_execution_failed", "dialog_title_select_test", "test_fcm_token_generation", "test_firestore_token_storage", "test_complete_notification_sync", "test_cloud_function_endpoint", "test_enhanced_notification", "test_debug_firestore_token_storage", "test_force_token_refresh", "test_quick_token_check", "text_running_test", "text_test_completed_successfully", "text_test_failed", "text_no_test_data_available", "text_test_passed", "text_test_failed_label", "rail_sathi_app_bar_title", "tab_write_complaint", "tab_view_complaints", "error_could_not_launch_link", "error_failed_to_load_issues", "error_invalid_response_format", "error_failed_to_load_trip_report", "msg_data_refreshed_successfully", "error_refresh_failed", "error_select_train_date_first", "text_no_coaches_found", "text_please_select_train_date", "text_coach_label", "text_no_issues_reported", "text_reported_issues", "text_unknown_issue", "btn_manage_issues", "text_train_rake_deficiency_report", "text_rake_deficiency_report_issues", "tooltip_upload_coach_image", "btn_upload_image_video", "btn_submit_issue", "form_pnr", "form_search_by_train_number_or_name", "form_dd_mm_yyyy", "btn_next", "tab_cleaning_issues", "tab_linen_related_issues", "msg_please_fill_all_required_details", "text_ehk_not_assigned", "msg_pnr_validation_error_e", "msg_are_you_sure_delete_image", "text_selected_date", "text_date", "text_pnr", "msg_are_you_sure_delete_complaint", "reports_app_bar_title", "btn_attendance_report", "btn_round_rake_deficiency_report", "btn_train_report", "btn_detailed_attendance_report", "btn_detailed_round_trip_attendance_report", "btn_obhs_to_mcc_handover", "btn_mcc_to_obhs_handover", "btn_monthly_attendance_report", "btn_monthly_round_trip_attendance_report", "text_daily_reports", "text_monthly_reports", "btn_show_all", "btn_show_less", "section_daily_reports", "section_monthly_reports", "section_attendance_for", "report_attendance", "report_detailed_attendance", "report_detailed_round_trip", "report_obhs_to_mcc_handover", "report_monthly_attendance", "report_monthly_round_trip", "report_monthly_with_mobile", "text_notification_settings", "text_save_settings", "text_onboarding_notifications", "text_enable_onboarding_notifications", "text_station_approach_alerts", "text_boarding_alerts", "text_off_boarding_alerts", "text_proximity_alerts", "text_timing_settings", "text_advance_notice_minutes", "text_proximity_threshold_km", "text_coach_filters", "text_enable_coach_specific_filtering", "text_enabled_coach_types", "text_sound_vibration", "text_enable_sound", "text_enable_vibration", "text_advanced_settings", "text_background_notifications", "text_location_based_notifications", "text_max_notifications_per_hour", "msg_notification_preferences_saved", "msg_error_saving_preferences", "error_fetching_trains", "form_mobile", "form_enter_train_number", "form_description", "dropdown_other", "dropdown_cleaning", "dropdown_linen", "dropdown_pending", "dropdown_completed", "dialog_delete_image", "dialog_delete_image_confirm", "dialog_delete_complaint", "dialog_delete_complaint_confirm", "btn_add_image", "btn_save_changes", "btn_edit", "btn_select_date", "text_edit_complaint", "form_train_label", "error_failed_to_fetch_complaints", "text_berth_no", "msg_image_deleted", "msg_complaint_updated", "msg_update_failed", "msg_complaint_deleted_success", "msg_failed_to_delete_complaint", "snackbar_image_deleted", "snackbar_failed_to_delete_image", "snackbar_failed_to_delete_complaint", "snackbar_failed_to_fetch_complaints", "btn_rake_deficiency_report_simple", "btn_monthly_with_mobile", "btn_monthly_without_mobile", "text_info", "text_reports_for", "text_trains_not_running", "text_train_not_running", "text_please_select_relevant_trains", "text_following_trains_not_running", "text_train_label", "text_running_days_label", "text_running_trains_on", "text_train_not_running_message", "pnr_status_title", "hint_enter_pnr_number", "label_pnr_number", "btn_check_pnr", "text_no_pnr_data_found", "error_invalid_pnr_number", "error_failed_fetch_pnr", "label_train_number", "label_train_name", "label_boarding_date", "label_from", "label_to", "label_class", "label_departure", "label_arrival", "label_overall_status", "label_booking_date", "label_passenger", "label_coach_berth", "label_ehk_users", "label_ca_users", "label_obhs_users"], "pa": ["text_reenter_otp", "text_deny", "text_enable", "text_location_access_required", "text_decline", "text_accept", "text_confirm_delete", "text_delete", "text_storage_permission_is", "text_please_select_a", "text_update_required", "text_update_now", "text_please_select_a_1", "text_all_details_updated", "text_failed_to_update", "text_data_refreshed_successfully", "text_train_location_saved", "text_self", "text_other_ca", "text_other_ehkobhs", "text_chart_has_not", "text_camera", "text_gallery", "text_getting_location", "text_error_message", "text_failed_to_get", "text_error_fetching_images", "text_back_to_all", "text_submit", "text_upload_status", "text_compressing_image", "text_upload_entrykeysubstring0_6", "text_image_uploading", "text_error_snapshoterror", "text_latitude_entrylatitude", "text_longitude_entrylongitude", "text_distance_entrydistance_km", "text_updated_by_entryupdatedby", "text_no_data_available", "text_show_more", "text_show_less", "text_could_not_open", "text_download_started", "text_pdf_downloaded_successfully", "text_download", "text_get_in_email", "text_could_not_launch", "text_permission_denied", "text_requested_users", "text_end_date_cannot", "text_please_select_both", "text_update_user_details", "text_request_for_update", "text_i_dont_have", "text_information", "text_request_for_sign", "text_forgot_password", "text_forgotten_password", "text_mobile_otp_login", "text_enter_otp", "text_otp_sent_to_mobile", "text_profile", "text_error", "text_sign_in_with", "text_new_user_sign", "text_please_enter_a", "text_failed_to_fetch", "text_check_pnr_status", "text_pnr_number", "text_no_pnr_data", "text_please_turn_on", "text_an_error_occurred", "text_your_current_location", "text_location_services_disabled", "text_please_enable_location", "text_location_permission_denied", "text_open_settings", "text_location_permission_denied_1", "text_refresh_failed_e", "text_no_location_data", "text_no_data_available_1", "text_no_train_details", "text_none", "text_download_pdf_for", "text_mail_pdf_for", "text_add_configuration", "text_select_charting_day", "text_submitting", "text_return_gap_updated", "text_data_not_refreshed", "text_add", "text_train_details_updated", "text_add_train_details", "text_add_train", "text_stoppages_in_sequence", "text_submitting_train_details", "text_edit_configuration", "text_no_coaches_available", "text_invalid_response_format", "text_please_select_a_2", "text_coach_handover_report", "text_save_selection", "text_select_media_type", "text_image", "text_video", "text_please_select_images", "text_please_select_at", "text_failed_to_upload", "text_error_updating_issue", "text_statustype_by", "text_no_images_available", "text_issuesubissue", "text_status", "text_submitupdate", "text_reportedby", "text_fixedby", "text_resolvedby", "text_pick_images", "text_uploading", "text_submit_upload", "text_retry", "text_approve", "text_no_requests_selected", "text_are_you_sure", "text_requeststoprocesslength_users_approved", "text_confirm_denial", "text_deny_request", "text_approve_selected", "text_approve_all", "text_processing_requests", "text_clear_search", "text_failed_to_fetch_1", "text_error_fetching_trains", "text_traintrainno_traintrainname", "text_other", "text_existing_images", "text_delete_image", "text_image_deleted", "text_newly_selected_images", "text_add_image", "text_complaint_updated", "text_update_failed", "text_save_changes", "text_delete_complaint", "text_are_you_sure_1", "text_complaint_deleted_successfully", "text_failed_to_delete", "text_select_date", "text_no_complaints_found", "text_train_no_complainttrainnumber", "text_date_complaintcomplaindate", "text_pnr_complaintpnrnumber", "text_edit", "text_complaint_submitted_successfully", "text_failed_to_submit", "text_error_e", "text_ehk_ehkdisplay", "text_pending", "text_completed", "text_upload_imagevideo", "text_submit_issue", "text_validate", "text_next", "text_error_loading_authentication", "text_storage_permission_required", "text_please_select_a_3", "text_error_json_file", "text_selection_cleared", "text_upload_json_data", "text_update", "text_add_issue", "text_no_subissues", "text_select_issue", "text_add_subissue", "text_add_new_item", "text_images_or_videos", "text_please_select_an", "text_issues_saved_upload", "text_select_issues_for", "text_pick_imagesvideos", "text_please_select_imagevideo", "text_confirm_deletion", "text_delete_report", "text_coach_issue_status", "text_both_person_and", "text_select_widgetstatustype_by", "text_subissue_widgetname", "text_issue_widgetname", "text_confirm", "text_manage_issues", "text_rake_deficiency_report", "text_upload_pnr_image", "text_pick_imagesvideos_for", "text_please_wait_until", "text_submit_feedback", "text_verify_email", "text_check_your_inbox", "text_if_not_found", "text_add_our_domain", "text_i_understand", "text_nonac", "text_select", "text_failed_to_load", "text_review_feedback", "text_deleting_feedback", "text_feedback_deleted_successfully", "text_error_deleting_feedback", "text_no_feedback_available", "text_train_no_trainnumber", "text_non_ac", "text_message", "text_job_chart_status", "text_please_select_all", "text_update_amount_for", "text_assigned", "text_amount", "text_no_image_url", "text_image_downloaded_successfully", "text_failed_to_download", "text_failed_to_download_1", "text_image_detail", "text_download_image", "text_train_trainnumber_details", "text_confirm_deactivation", "text_proceed", "text_add_email", "text_back", "text_email_verification", "text_phone_verification", "text_logout_confirmation", "text_do_you_want", "text_addupdate", "text_otp_sent_successfully", "text_failed_to_send", "text_please_enter_the", "text_failed_to_verify", "text_uploaded_at_formatteddate", "text_uploaded_by_widgetimageresponsecreatedby", "text_id_widgetimageresponseid", "text_coach_widgetimageresponsecoach", "text_issue_widgetimageresponseissue", "text_delete_confirmation", "text_are_you_sure_2", "text_save", "text_ehkca", "text_image_upload_initiated", "text_failed_to_upload_1", "text_please_select_an_1", "text_jobchart_deleted_successfully", "text_failed_to_delete_1", "text_pick_image", "text_upload_image", "text_failed_to_load_1", "text_language", "text_select_language", "text_rail_sathi", "title_location_access_required", "title_upload_status", "title_compressing_image", "title_upload_entrykeysubstring0_6", "title_permission_denied", "title_confirm_delete", "title_add_new_item", "title_add_issue", "title_add_subissue", "title_select_widgetstatustype_by", "title_update_amount_for", "form_train_number", "form_date", "form_select_date_ddmmmyyyy", "form_first_name", "form_enter_first_name", "form_middle_name_optional", "form_enter_middle_name", "form_last_name", "form_enter_last_name", "form_phone_number", "form_secondary_phone_number", "form_whatsapp_number", "form_enter_10digit_whatsapp", "form_email", "form_enter_your_email", "form_enter_your_first", "form_enter_your_middle", "form_enter_your_last", "form_enter_10digit_secondary", "form_email_1", "form_enter_your_10digit", "form_select_train_numbers", "form_zone", "form_zone_1", "form_employee_id", "form_depot", "form_reenter_password", "form_divisions", "form_divisions_1", "form_select_coaches", "form_middle_name", "form_select_train_number", "form_select_stations", "form_select_date", "form_whatsapp_number_1", "form_enter_secondary_phone", "form_mobile_number_1", "form_enter_mobile_number", "form_enter_mobile_number_1", "form_related_train", "form_division", "form_depot_1", "form_charting_day", "form_from_station", "form_to_station", "form_direction_updown", "form_start_time", "form_eg_0900_am", "form_end_time", "form_eg_0500_pm", "form_charting_time", "form_return_gap_days", "form_inout", "form_related_train_number", "form_updown", "form_train_type", "form_search_train_number", "form_coaches_comma_separated", "form_eg_h_gsl", "form_enter_coach_names", "form_select_days", "form_add_stoppage", "form_type_a_station", "form_search", "form_stoppages_in_sequence", "form_type_a_station_1", "form_frequency", "form_enter_new_coach", "form_use_comma_to", "form_add_your_comments", "form_search_by_name", "form_train", "form_complaint_type", "form_status", "form_write_your_issue", "form_issue_status", "form_name", "form_search_by_train", "form_train_selection", "form_journey_start_date", "form_ddmmyyyy", "form_coach", "form_berth", "form_issue_name", "form_subissue_name", "form_search_1", "form_widgetstatustype_by", "form_select_date_time", "form_add_your_feedback", "form_search_train_number_1", "form_train_number_1", "form_enter_otp", "form_resolved_yesno", "form_train_name_1", "form_marks_1_to", "form_remarks_by_passenger", "form_passenger_name_1", "form_pnr_number_1", "form_crn_number_1", "form_coach_no_1", "form_berth_no_1", "form_task_status_1", "form_feedback", "form_amount_in_hand", "form_select_user", "form_enter_email_otp", "form_enter_phone_otp", "form_select_coaches_optional", "btn_deny", "btn_enable", "btn_decline", "btn_accept", "btn_self", "btn_other_ca", "btn_other_ehkobhs", "btn_close", "btn_error_snapshoterror", "btn_no_data_available", "btn_your_current_location", "btn_no_location_data", "btn_no_data_available_1", "btn_add_configuration", "btn_select_charting_day", "btn_edit_configuration", "btn_no_coaches_available", "btn_coach_handover_report", "btn_statustype_by", "btn_traintrainno_traintrainname", "btn_other", "btn_no_complaints_found", "btn_pending", "btn_completed", "btn_upload_json_data", "btn_cancel", "btn_delete", "btn_save_selection", "btn_coach_issue_status", "btn_rake_deficiency_report", "btn_no_feedback_available", "btn_save", "snackbar_please_select_a", "snackbar_data_refreshed_successfully", "snackbar_train_location_saved", "snackbar_error_fetching_images", "snackbar_download_started", "snackbar_pdf_downloaded_successfully", "snackbar_could_not_launch", "snackbar_refresh_failed_e", "snackbar_return_gap_updated", "snackbar_data_not_refreshed", "snackbar_invalid_response_format", "snackbar_please_select_a_1", "snackbar_please_select_images", "snackbar_please_select_at", "snackbar_failed_to_upload", "snackbar_error_updating_issue", "snackbar_no_images_available", "snackbar_failed_to_fetch", "snackbar_error_fetching_trains", "snackbar_complaint_updated", "snackbar_update_failed", "snackbar_complaint_deleted_successfully", "snackbar_failed_to_delete", "snackbar_failed_to_submit", "snackbar_error_e", "snackbar_issues_saved_upload", "snackbar_feedback_deleted_successfully", "snackbar_error_deleting_feedback", "snackbar_job_chart_status", "snackbar_no_image_url", "snackbar_image_downloaded_successfully", "snackbar_failed_to_download", "snackbar_failed_to_download_1", "snackbar_otp_sent_successfully", "snackbar_failed_to_send", "snackbar_please_enter_the", "snackbar_email_saved_successfully", "snackbar_failed_to_verify", "snackbar_jobchart_deleted_successfully", "snackbar_failed_to_delete_1", "snackbar_failed_to_load", "text_train_details", "text_please_enable_location_services", "text_location_permissions_required", "text_location_permission_denied_forever", "text_location_permissions_permanently_denied", "text_select_all", "text_location_permissions_error", "text_refresh_failed", "text_turn_on_location_services", "text_no_location_data_available", "text_edit_train_details", "text_search_train_number", "text_select_train_number", "text_monday", "text_tuesday", "text_wednesday", "text_thursday", "text_friday", "text_saturday", "text_sunday", "text_updating_data", "text_return_gap_updated_successfully", "text_train_details_updated_successfully", "text_restrictions", "text_location", "text_time", "text_enable_media_upload", "text_failed_to_load_zones", "text_failed_to_load_divisions", "text_failed_to_load_depots", "text_failed_to_load_train_details", "text_error_loading_train_data", "text_edit_profile", "text_error_loading_authentication_state", "text_verify_email_button", "text_first_name", "text_middle_name", "text_last_name", "text_employee_number", "text_role", "text_depot", "text_phone_number", "text_whatsapp_number", "text_secondary_phone_number", "text_email", "text_phone_number_must_be_10_digits", "text_phone_number_and_secondary_phone_number_must_be_different", "text_change_email", "text_change_password", "text_change_mobile_number", "text_change_whatsapp_number", "text_update_profile", "text_add_trains", "text_up", "text_down", "text_today", "text_yesterday", "text_failed_to_load_zones_add", "text_failed_to_load_divisions_add", "text_failed_to_load_depots_add", "text_please_enter_train_number", "text_train_number_min_digits", "text_please_select_return_gap", "text_coaches_sequence", "text_please_enter_train_name", "text_please_select_charting_day", "text_please_enter_from_station", "text_please_enter_to_station", "text_please_select_direction", "text_please_enter_start_time", "text_please_enter_end_time", "text_please_enter_charting_time", "text_please_enter_train_type", "text_add_new_user", "text_phone_and_secondary_phone_must_be_different", "text_validation_error", "text_submitting_data_please_wait", "text_please_complete_all_required_fields", "text_form_incomplete", "text_enter_first_name", "text_please_enter_first_name", "text_middle_name_optional", "text_enter_middle_name", "text_enter_last_name", "text_please_enter_last_name", "text_secondary_phone_number_optional", "text_whatsapp_number_same_as_phone", "text_use_same_number_for_whatsapp", "text_enter_10_digit_whatsapp_number", "text_please_enter_whatsapp_number", "text_whatsapp_number_must_be_10_digits", "text_please_enter_only_numbers", "text_request_for_add_user", "text_information_dialog_title", "text_please_complete_fields_in_order", "text_personal_information", "text_contact_details", "text_account_settings", "text_authentication_check_failed", "text_enter_a_valid_mobile_number", "text_user_data_not_found", "text_access_token_not_found", "text_update_user", "text_enter_mobile_number", "text_search_for_a_user_to_update_their_details", "text_user_not_found_please_check_the_mobile_number_and_try_again", "text_error_with_details", "text_please_enter_your_email", "text_please_enter_valid_email", "text_invalid_email_format", "text_please_enter_valid_mobile_number", "text_please_enter_new_mobile_number", "text_do_you_want_to_logout", "text_yes", "text_no", "text_inside_train", "text_need_alarm", "text_deactivate", "text_logout", "text_email_otp_error", "text_phone_otp_error", "text_please_select_a_train_number", "text_add_update", "text_an_error_occurred_while_adding", "text_select_coaches_optional", "text_select_coaches_from_list", "text_please_select_date", "text_need_valid_email_before_deactivation", "text_send_otps_for_verification", "text_failed_to_send_otps", "text_enter_email_otp", "text_enter_phone_otp", "text_please_enter_field", "text_enable_fingerprint_login", "text_deactivate_account", "text_email_verification_optional", "text_verified", "text_email_verification_info", "text_otp_sent_message", "error_berth_number_invalid", "error_form_validation_failed", "error_max_videos", "error_max_videos_short", "error_max_feedback_images", "error_max_feedback_images_short", "error_max_images_reached", "error_picking_media", "error_email_required", "msg_email_verification_initiated", "error_otp_required", "error_something_wrong", "text_daily", "text_yes_lowercase", "text_no_lowercase", "error_berth_number_invalid_passenger", "error_no_train_selected", "error_fetching_charting_time", "dialog_train_not_running_content", "error_validating_pnr", "error_feedback_submission_passenger", "error_max_images_reached_passenger", "error_picking_media_passenger", "error_generic_passenger", "error_something_wrong_passenger", "text_check_inbox", "text_check_spam", "text_add_safe_sender", "msg_otp_sent_check_folders", "msg_error_generic", "msg_something_went_wrong", "status_yes_lowercase", "status_no_lowercase", "passenger_feedback_app_bar_title", "passenger_feedback_main_title", "form_train_name_readonly", "hint_search_train_number", "category_ac", "category_nonac", "category_tt", "category_rm", "rating_excellent", "rating_very_good", "rating_good", "rating_average", "rating_poor", "feedback_table_instruction", "feedback_table_item_header", "dialog_train_not_running", "msg_train_not_running_details", "msg_running_days_daily", "msg_running_days_na", "validation_provide_rating", "msg_rm_fields_required", "msg_upload_complete_wait", "msg_videos_limit", "msg_videos_limit_reached", "feedback_ac_item_1", "feedback_ac_item_2", "feedback_ac_item_3", "feedback_ac_item_4", "feedback_ac_item_5", "feedback_nonac_item_1", "feedback_nonac_item_2", "feedback_nonac_item_3", "button_ok", "error_fetch_train_name", "error_invalid_pnr", "success_pnr_fetched", "error_pnr_validation", "success_feedback_submitted", "error_feedback_submission", "error_unexpected", "email_verification_info_title", "email_verification_spam_notice", "email_verification_after_otp", "email_verification_understand", "success_email_verification", "error_prefix", "success_otp_verified", "upload_pnr_image", "selected_images", "no_pnr_images_selected", "upload_limit_pnr_3", "media_source_camera", "media_source_gallery", "upload_limit_pnr_max", "upload_limit_reached_message", "error_picking_media_simple", "pick_images_videos_feedback", "no_feedback_images_selected", "selected_videos", "select_media_type", "media_type_image", "media_type_video", "upload_limit_videos_3", "upload_limit_videos_max", "upload_limit_feedback_images_3", "upload_limit_feedback_images_max", "text_pnr_document", "text_feedback_media", "loading_deleting_feedback", "success_feedback_deleted", "error_deleting_feedback", "text_train_no", "text_not_verified", "btn_review", "btn_retry", "msg_no_feedback_available", "dialog_title_review_feedback", "text_status_pending_editable", "text_status_completed_not_editable", "text_pnr_documents", "text_failed_to_load_image", "text_tap_to_play_video", "form_remarks_hint", "form_resolved_yes_no", "form_marks_1_to_10", "status_none_selected", "btn_update", "validation_select_issue_type", "validation_select_sub_issue_type", "validation_select_resolved_status", "validation_select_marks", "validation_enter_valid_email", "validation_enter_otp", "validation_remarks_max_100", "msg_cannot_verify_email_completed", "msg_cannot_verify_otp_completed", "msg_otp_verified_successfully", "msg_feedback_updated_successfully", "msg_failed_to_update_feedback", "msg_failed_to_load_video", "msg_error_with_details", "msg_error_or_something_wrong", "loading_updating_feedback", "text_character_count_100", "dialog_email_verification_info_title", "dialog_email_verification_spam_notice", "dialog_after_requesting_otp", "dialog_check_inbox_first", "dialog_check_spam_folder", "dialog_add_safe_sender", "text_otp_sent_check_folders", "form_issue_type_label", "form_sub_issue_type_label", "form_task_status_label", "status_pending_option", "status_completed_option", "text_need_help_reach_out", "error_could_not_launch_phone", "text_notifications", "tooltip_test_notifications", "tooltip_notification_settings", "tooltip_mark_all_as_read", "tooltip_clear_all_notifications", "text_no_notifications", "dialog_title_clear_notifications", "dialog_content_clear_notifications", "btn_clear", "text_notification_fallback", "text_coach_details", "table_header_station", "table_header_coach", "table_header_onboard", "table_header_deboard", "table_header_vacant", "text_notification_system_tests", "text_notification_system_testing", "text_notification_test_description", "text_ready_to_run_notification_tests", "btn_run_all_tests", "btn_running_tests", "btn_individual_tests", "text_test_results", "text_no_test_results_yet", "text_run_tests_to_see_results", "text_running_comprehensive_tests", "text_all_tests_completed_successfully", "text_some_tests_failed", "text_test_execution_failed", "dialog_title_select_test", "test_fcm_token_generation", "test_firestore_token_storage", "test_complete_notification_sync", "test_cloud_function_endpoint", "test_enhanced_notification", "test_debug_firestore_token_storage", "test_force_token_refresh", "test_quick_token_check", "text_running_test", "text_test_completed_successfully", "text_test_failed", "text_no_test_data_available", "text_test_passed", "text_test_failed_label", "rail_sathi_app_bar_title", "tab_write_complaint", "tab_view_complaints", "error_could_not_launch_link", "error_failed_to_load_issues", "error_invalid_response_format", "error_failed_to_load_trip_report", "msg_data_refreshed_successfully", "error_refresh_failed", "error_select_train_date_first", "text_no_coaches_found", "text_please_select_train_date", "text_coach_label", "text_no_issues_reported", "text_reported_issues", "text_unknown_issue", "btn_manage_issues", "text_train_rake_deficiency_report", "text_rake_deficiency_report_issues", "tooltip_upload_coach_image", "btn_upload_image_video", "btn_submit_issue", "form_pnr", "form_search_by_train_number_or_name", "form_dd_mm_yyyy", "btn_next", "tab_cleaning_issues", "tab_linen_related_issues", "msg_please_fill_all_required_details", "text_ehk_not_assigned", "msg_pnr_validation_error_e", "msg_are_you_sure_delete_image", "text_selected_date", "text_date", "text_pnr", "msg_are_you_sure_delete_complaint", "reports_app_bar_title", "btn_attendance_report", "btn_round_rake_deficiency_report", "btn_train_report", "btn_detailed_attendance_report", "btn_detailed_round_trip_attendance_report", "btn_obhs_to_mcc_handover", "btn_mcc_to_obhs_handover", "btn_monthly_attendance_report", "btn_monthly_round_trip_attendance_report", "text_daily_reports", "text_monthly_reports", "btn_show_all", "btn_show_less", "section_daily_reports", "section_monthly_reports", "section_attendance_for", "report_attendance", "report_detailed_attendance", "report_detailed_round_trip", "report_obhs_to_mcc_handover", "report_monthly_attendance", "report_monthly_round_trip", "report_monthly_with_mobile", "text_notification_settings", "text_save_settings", "text_onboarding_notifications", "text_enable_onboarding_notifications", "text_station_approach_alerts", "text_boarding_alerts", "text_off_boarding_alerts", "text_proximity_alerts", "text_timing_settings", "text_advance_notice_minutes", "text_proximity_threshold_km", "text_coach_filters", "text_enable_coach_specific_filtering", "text_enabled_coach_types", "text_sound_vibration", "text_enable_sound", "text_enable_vibration", "text_advanced_settings", "text_background_notifications", "text_location_based_notifications", "text_max_notifications_per_hour", "msg_notification_preferences_saved", "msg_error_saving_preferences", "error_fetching_trains", "form_mobile", "form_enter_train_number", "form_description", "dropdown_other", "dropdown_cleaning", "dropdown_linen", "dropdown_pending", "dropdown_completed", "dialog_delete_image", "dialog_delete_image_confirm", "dialog_delete_complaint", "dialog_delete_complaint_confirm", "btn_add_image", "btn_save_changes", "btn_edit", "btn_select_date", "text_edit_complaint", "form_train_label", "error_failed_to_fetch_complaints", "text_berth_no", "msg_image_deleted", "msg_complaint_updated", "msg_update_failed", "msg_complaint_deleted_success", "msg_failed_to_delete_complaint", "snackbar_image_deleted", "snackbar_failed_to_delete_image", "snackbar_failed_to_delete_complaint", "snackbar_failed_to_fetch_complaints", "btn_rake_deficiency_report_simple", "btn_monthly_with_mobile", "btn_monthly_without_mobile", "text_info", "text_reports_for", "text_trains_not_running", "text_train_not_running", "text_please_select_relevant_trains", "text_following_trains_not_running", "text_train_label", "text_running_days_label", "text_running_trains_on", "text_train_not_running_message", "pnr_status_title", "hint_enter_pnr_number", "label_pnr_number", "btn_check_pnr", "text_no_pnr_data_found", "error_invalid_pnr_number", "error_failed_fetch_pnr", "label_train_number", "label_train_name", "label_boarding_date", "label_from", "label_to", "label_class", "label_departure", "label_arrival", "label_overall_status", "label_booking_date", "label_passenger", "label_coach_berth", "label_ehk_users", "label_ca_users", "label_obhs_users"], "ta": ["text_reenter_otp", "text_deny", "text_enable", "text_location_access_required", "text_decline", "text_accept", "text_confirm_delete", "text_delete", "text_storage_permission_is", "text_please_select_a", "text_update_required", "text_update_now", "text_please_select_a_1", "text_all_details_updated", "text_failed_to_update", "text_data_refreshed_successfully", "text_train_location_saved", "text_self", "text_other_ca", "text_other_ehkobhs", "text_chart_has_not", "text_camera", "text_gallery", "text_getting_location", "text_error_message", "text_failed_to_get", "text_error_fetching_images", "text_back_to_all", "text_submit", "text_upload_status", "text_compressing_image", "text_upload_entrykeysubstring0_6", "text_image_uploading", "text_error_snapshoterror", "text_latitude_entrylatitude", "text_longitude_entrylongitude", "text_distance_entrydistance_km", "text_updated_by_entryupdatedby", "text_no_data_available", "text_show_more", "text_show_less", "text_could_not_open", "text_download_started", "text_pdf_downloaded_successfully", "text_download", "text_get_in_email", "text_could_not_launch", "text_permission_denied", "text_requested_users", "text_end_date_cannot", "text_please_select_both", "text_update_user_details", "text_request_for_update", "text_i_dont_have", "text_information", "text_request_for_sign", "text_forgot_password", "text_forgotten_password", "text_mobile_otp_login", "text_enter_otp", "text_otp_sent_to_mobile", "text_profile", "text_error", "text_sign_in_with", "text_new_user_sign", "text_please_enter_a", "text_failed_to_fetch", "text_check_pnr_status", "text_pnr_number", "text_no_pnr_data", "text_please_turn_on", "text_an_error_occurred", "text_your_current_location", "text_location_services_disabled", "text_please_enable_location", "text_location_permission_denied", "text_open_settings", "text_location_permission_denied_1", "text_refresh_failed_e", "text_no_location_data", "text_no_data_available_1", "text_no_train_details", "text_none", "text_download_pdf_for", "text_mail_pdf_for", "text_add_configuration", "text_select_charting_day", "text_submitting", "text_return_gap_updated", "text_data_not_refreshed", "text_add", "text_train_details_updated", "text_add_train_details", "text_add_train", "text_stoppages_in_sequence", "text_submitting_train_details", "text_edit_configuration", "text_no_coaches_available", "text_invalid_response_format", "text_please_select_a_2", "text_coach_handover_report", "text_save_selection", "text_select_media_type", "text_image", "text_video", "text_please_select_images", "text_please_select_at", "text_failed_to_upload", "text_error_updating_issue", "text_statustype_by", "text_no_images_available", "text_issuesubissue", "text_status", "text_submitupdate", "text_reportedby", "text_fixedby", "text_resolvedby", "text_pick_images", "text_uploading", "text_submit_upload", "text_retry", "text_approve", "text_no_requests_selected", "text_are_you_sure", "text_requeststoprocesslength_users_approved", "text_confirm_denial", "text_deny_request", "text_approve_selected", "text_approve_all", "text_processing_requests", "text_clear_search", "text_failed_to_fetch_1", "text_error_fetching_trains", "text_traintrainno_traintrainname", "text_other", "text_existing_images", "text_delete_image", "text_image_deleted", "text_newly_selected_images", "text_add_image", "text_complaint_updated", "text_update_failed", "text_save_changes", "text_delete_complaint", "text_are_you_sure_1", "text_complaint_deleted_successfully", "text_failed_to_delete", "text_select_date", "text_no_complaints_found", "text_train_no_complainttrainnumber", "text_date_complaintcomplaindate", "text_pnr_complaintpnrnumber", "text_edit", "text_complaint_submitted_successfully", "text_failed_to_submit", "text_error_e", "text_ehk_ehkdisplay", "text_pending", "text_completed", "text_upload_imagevideo", "text_submit_issue", "text_validate", "text_next", "text_error_loading_authentication", "text_storage_permission_required", "text_please_select_a_3", "text_error_json_file", "text_selection_cleared", "text_upload_json_data", "text_update", "text_add_issue", "text_no_subissues", "text_select_issue", "text_add_subissue", "text_add_new_item", "text_images_or_videos", "text_please_select_an", "text_issues_saved_upload", "text_select_issues_for", "text_pick_imagesvideos", "text_please_select_imagevideo", "text_confirm_deletion", "text_delete_report", "text_coach_issue_status", "text_both_person_and", "text_select_widgetstatustype_by", "text_subissue_widgetname", "text_issue_widgetname", "text_confirm", "text_manage_issues", "text_rake_deficiency_report", "text_upload_pnr_image", "text_pick_imagesvideos_for", "text_please_wait_until", "text_submit_feedback", "text_verify_email", "text_check_your_inbox", "text_if_not_found", "text_add_our_domain", "text_i_understand", "text_nonac", "text_select", "text_failed_to_load", "text_review_feedback", "text_deleting_feedback", "text_feedback_deleted_successfully", "text_error_deleting_feedback", "text_no_feedback_available", "text_train_no_trainnumber", "text_non_ac", "text_message", "text_job_chart_status", "text_please_select_all", "text_update_amount_for", "text_assigned", "text_amount", "text_no_image_url", "text_image_downloaded_successfully", "text_failed_to_download", "text_failed_to_download_1", "text_image_detail", "text_download_image", "text_train_trainnumber_details", "text_confirm_deactivation", "text_proceed", "text_add_email", "text_back", "text_email_verification", "text_phone_verification", "text_logout_confirmation", "text_do_you_want", "text_addupdate", "text_otp_sent_successfully", "text_failed_to_send", "text_please_enter_the", "text_failed_to_verify", "text_uploaded_at_formatteddate", "text_uploaded_by_widgetimageresponsecreatedby", "text_id_widgetimageresponseid", "text_coach_widgetimageresponsecoach", "text_issue_widgetimageresponseissue", "text_delete_confirmation", "text_are_you_sure_2", "text_save", "text_ehkca", "text_image_upload_initiated", "text_failed_to_upload_1", "text_please_select_an_1", "text_jobchart_deleted_successfully", "text_failed_to_delete_1", "text_pick_image", "text_upload_image", "text_failed_to_load_1", "text_language", "text_select_language", "text_rail_sathi", "title_location_access_required", "title_upload_status", "title_compressing_image", "title_upload_entrykeysubstring0_6", "title_permission_denied", "title_confirm_delete", "title_add_new_item", "title_add_issue", "title_add_subissue", "title_select_widgetstatustype_by", "title_update_amount_for", "form_train_number", "form_date", "form_select_date_ddmmmyyyy", "form_first_name", "form_enter_first_name", "form_middle_name_optional", "form_enter_middle_name", "form_last_name", "form_enter_last_name", "form_phone_number", "form_secondary_phone_number", "form_whatsapp_number", "form_enter_10digit_whatsapp", "form_email", "form_enter_your_email", "form_enter_your_first", "form_enter_your_middle", "form_enter_your_last", "form_enter_10digit_secondary", "form_email_1", "form_enter_your_10digit", "form_select_train_numbers", "form_zone", "form_zone_1", "form_employee_id", "form_depot", "form_reenter_password", "form_divisions", "form_divisions_1", "form_select_coaches", "form_middle_name", "form_select_train_number", "form_select_stations", "form_select_date", "form_whatsapp_number_1", "form_enter_secondary_phone", "form_mobile_number_1", "form_enter_mobile_number", "form_enter_mobile_number_1", "form_related_train", "form_division", "form_depot_1", "form_charting_day", "form_from_station", "form_to_station", "form_direction_updown", "form_start_time", "form_eg_0900_am", "form_end_time", "form_eg_0500_pm", "form_charting_time", "form_return_gap_days", "form_inout", "form_related_train_number", "form_updown", "form_train_type", "form_search_train_number", "form_coaches_comma_separated", "form_eg_h_gsl", "form_enter_coach_names", "form_select_days", "form_add_stoppage", "form_type_a_station", "form_search", "form_stoppages_in_sequence", "form_type_a_station_1", "form_frequency", "form_enter_new_coach", "form_use_comma_to", "form_add_your_comments", "form_search_by_name", "form_train", "form_complaint_type", "form_status", "form_write_your_issue", "form_issue_status", "form_name", "form_search_by_train", "form_train_selection", "form_journey_start_date", "form_ddmmyyyy", "form_coach", "form_berth", "form_issue_name", "form_subissue_name", "form_search_1", "form_widgetstatustype_by", "form_select_date_time", "form_add_your_feedback", "form_search_train_number_1", "form_train_number_1", "form_enter_otp", "form_resolved_yesno", "form_train_name_1", "form_marks_1_to", "form_remarks_by_passenger", "form_passenger_name_1", "form_pnr_number_1", "form_crn_number_1", "form_coach_no_1", "form_berth_no_1", "form_task_status_1", "form_feedback", "form_amount_in_hand", "form_select_user", "form_enter_email_otp", "form_enter_phone_otp", "form_select_coaches_optional", "btn_deny", "btn_enable", "btn_decline", "btn_accept", "btn_self", "btn_other_ca", "btn_other_ehkobhs", "btn_close", "btn_error_snapshoterror", "btn_no_data_available", "btn_your_current_location", "btn_no_location_data", "btn_no_data_available_1", "btn_add_configuration", "btn_select_charting_day", "btn_edit_configuration", "btn_no_coaches_available", "btn_coach_handover_report", "btn_statustype_by", "btn_traintrainno_traintrainname", "btn_other", "btn_no_complaints_found", "btn_pending", "btn_completed", "btn_upload_json_data", "btn_cancel", "btn_delete", "btn_save_selection", "btn_coach_issue_status", "btn_rake_deficiency_report", "btn_no_feedback_available", "btn_save", "snackbar_please_select_a", "snackbar_data_refreshed_successfully", "snackbar_train_location_saved", "snackbar_error_fetching_images", "snackbar_download_started", "snackbar_pdf_downloaded_successfully", "snackbar_could_not_launch", "snackbar_refresh_failed_e", "snackbar_return_gap_updated", "snackbar_data_not_refreshed", "snackbar_invalid_response_format", "snackbar_please_select_a_1", "snackbar_please_select_images", "snackbar_please_select_at", "snackbar_failed_to_upload", "snackbar_error_updating_issue", "snackbar_no_images_available", "snackbar_failed_to_fetch", "snackbar_error_fetching_trains", "snackbar_complaint_updated", "snackbar_update_failed", "snackbar_complaint_deleted_successfully", "snackbar_failed_to_delete", "snackbar_failed_to_submit", "snackbar_error_e", "snackbar_issues_saved_upload", "snackbar_feedback_deleted_successfully", "snackbar_error_deleting_feedback", "snackbar_job_chart_status", "snackbar_no_image_url", "snackbar_image_downloaded_successfully", "snackbar_failed_to_download", "snackbar_failed_to_download_1", "snackbar_otp_sent_successfully", "snackbar_failed_to_send", "snackbar_please_enter_the", "snackbar_email_saved_successfully", "snackbar_failed_to_verify", "snackbar_jobchart_deleted_successfully", "snackbar_failed_to_delete_1", "snackbar_failed_to_load", "text_train_details", "text_please_enable_location_services", "text_location_permissions_required", "text_location_permission_denied_forever", "text_location_permissions_permanently_denied", "text_select_all", "text_location_permissions_error", "text_refresh_failed", "text_turn_on_location_services", "text_no_location_data_available", "text_edit_train_details", "text_search_train_number", "text_select_train_number", "text_monday", "text_tuesday", "text_wednesday", "text_thursday", "text_friday", "text_saturday", "text_sunday", "text_updating_data", "text_return_gap_updated_successfully", "text_train_details_updated_successfully", "text_restrictions", "text_location", "text_time", "text_enable_media_upload", "text_failed_to_load_zones", "text_failed_to_load_divisions", "text_failed_to_load_depots", "text_failed_to_load_train_details", "text_error_loading_train_data", "text_edit_profile", "text_error_loading_authentication_state", "text_verify_email_button", "text_first_name", "text_middle_name", "text_last_name", "text_employee_number", "text_role", "text_depot", "text_phone_number", "text_whatsapp_number", "text_secondary_phone_number", "text_email", "text_phone_number_must_be_10_digits", "text_phone_number_and_secondary_phone_number_must_be_different", "text_change_email", "text_change_password", "text_change_mobile_number", "text_change_whatsapp_number", "text_update_profile", "text_add_trains", "text_up", "text_down", "text_today", "text_yesterday", "text_failed_to_load_zones_add", "text_failed_to_load_divisions_add", "text_failed_to_load_depots_add", "text_please_enter_train_number", "text_train_number_min_digits", "text_please_select_return_gap", "text_coaches_sequence", "text_please_enter_train_name", "text_please_select_charting_day", "text_please_enter_from_station", "text_please_enter_to_station", "text_please_select_direction", "text_please_enter_start_time", "text_please_enter_end_time", "text_please_enter_charting_time", "text_please_enter_train_type", "text_add_new_user", "text_phone_and_secondary_phone_must_be_different", "text_validation_error", "text_submitting_data_please_wait", "text_please_complete_all_required_fields", "text_form_incomplete", "text_enter_first_name", "text_please_enter_first_name", "text_middle_name_optional", "text_enter_middle_name", "text_enter_last_name", "text_please_enter_last_name", "text_secondary_phone_number_optional", "text_whatsapp_number_same_as_phone", "text_use_same_number_for_whatsapp", "text_enter_10_digit_whatsapp_number", "text_please_enter_whatsapp_number", "text_whatsapp_number_must_be_10_digits", "text_please_enter_only_numbers", "text_request_for_add_user", "text_information_dialog_title", "text_please_complete_fields_in_order", "text_personal_information", "text_contact_details", "text_account_settings", "text_authentication_check_failed", "text_enter_a_valid_mobile_number", "text_user_data_not_found", "text_access_token_not_found", "text_update_user", "text_enter_mobile_number", "text_search_for_a_user_to_update_their_details", "text_user_not_found_please_check_the_mobile_number_and_try_again", "text_error_with_details", "text_please_enter_your_email", "text_please_enter_valid_email", "text_invalid_email_format", "text_please_enter_valid_mobile_number", "text_please_enter_new_mobile_number", "text_do_you_want_to_logout", "text_yes", "text_no", "text_inside_train", "text_need_alarm", "text_deactivate", "text_logout", "text_email_otp_error", "text_phone_otp_error", "text_please_select_a_train_number", "text_add_update", "text_an_error_occurred_while_adding", "text_select_coaches_optional", "text_select_coaches_from_list", "text_please_select_date", "text_need_valid_email_before_deactivation", "text_send_otps_for_verification", "text_failed_to_send_otps", "text_enter_email_otp", "text_enter_phone_otp", "text_please_enter_field", "text_enable_fingerprint_login", "text_deactivate_account", "text_email_verification_optional", "text_verified", "text_email_verification_info", "text_otp_sent_message", "error_berth_number_invalid", "error_form_validation_failed", "error_max_videos", "error_max_videos_short", "error_max_feedback_images", "error_max_feedback_images_short", "error_max_images_reached", "error_picking_media", "error_email_required", "msg_email_verification_initiated", "error_otp_required", "error_something_wrong", "text_daily", "text_yes_lowercase", "text_no_lowercase", "error_berth_number_invalid_passenger", "error_no_train_selected", "error_fetching_charting_time", "dialog_train_not_running_content", "error_validating_pnr", "error_feedback_submission_passenger", "error_max_images_reached_passenger", "error_picking_media_passenger", "error_generic_passenger", "error_something_wrong_passenger", "text_check_inbox", "text_check_spam", "text_add_safe_sender", "msg_otp_sent_check_folders", "msg_error_generic", "msg_something_went_wrong", "status_yes_lowercase", "status_no_lowercase", "passenger_feedback_app_bar_title", "passenger_feedback_main_title", "form_train_name_readonly", "hint_search_train_number", "category_ac", "category_nonac", "category_tt", "category_rm", "rating_excellent", "rating_very_good", "rating_good", "rating_average", "rating_poor", "feedback_table_instruction", "feedback_table_item_header", "dialog_train_not_running", "msg_train_not_running_details", "msg_running_days_daily", "msg_running_days_na", "validation_provide_rating", "msg_rm_fields_required", "msg_upload_complete_wait", "msg_videos_limit", "msg_videos_limit_reached", "feedback_ac_item_1", "feedback_ac_item_2", "feedback_ac_item_3", "feedback_ac_item_4", "feedback_ac_item_5", "feedback_nonac_item_1", "feedback_nonac_item_2", "feedback_nonac_item_3", "button_ok", "error_fetch_train_name", "error_invalid_pnr", "success_pnr_fetched", "error_pnr_validation", "success_feedback_submitted", "error_feedback_submission", "error_unexpected", "email_verification_info_title", "email_verification_spam_notice", "email_verification_after_otp", "email_verification_understand", "success_email_verification", "error_prefix", "success_otp_verified", "upload_pnr_image", "selected_images", "no_pnr_images_selected", "upload_limit_pnr_3", "media_source_camera", "media_source_gallery", "upload_limit_pnr_max", "upload_limit_reached_message", "error_picking_media_simple", "pick_images_videos_feedback", "no_feedback_images_selected", "selected_videos", "select_media_type", "media_type_image", "media_type_video", "upload_limit_videos_3", "upload_limit_videos_max", "upload_limit_feedback_images_3", "upload_limit_feedback_images_max", "text_pnr_document", "text_feedback_media", "loading_deleting_feedback", "success_feedback_deleted", "error_deleting_feedback", "text_train_no", "text_not_verified", "btn_review", "btn_retry", "msg_no_feedback_available", "dialog_title_review_feedback", "text_status_pending_editable", "text_status_completed_not_editable", "text_pnr_documents", "text_failed_to_load_image", "text_tap_to_play_video", "form_remarks_hint", "form_resolved_yes_no", "form_marks_1_to_10", "status_none_selected", "btn_update", "validation_select_issue_type", "validation_select_sub_issue_type", "validation_select_resolved_status", "validation_select_marks", "validation_enter_valid_email", "validation_enter_otp", "validation_remarks_max_100", "msg_cannot_verify_email_completed", "msg_cannot_verify_otp_completed", "msg_otp_verified_successfully", "msg_feedback_updated_successfully", "msg_failed_to_update_feedback", "msg_failed_to_load_video", "msg_error_with_details", "msg_error_or_something_wrong", "loading_updating_feedback", "text_character_count_100", "dialog_email_verification_info_title", "dialog_email_verification_spam_notice", "dialog_after_requesting_otp", "dialog_check_inbox_first", "dialog_check_spam_folder", "dialog_add_safe_sender", "text_otp_sent_check_folders", "form_issue_type_label", "form_sub_issue_type_label", "form_task_status_label", "status_pending_option", "status_completed_option", "text_need_help_reach_out", "error_could_not_launch_phone", "text_notifications", "tooltip_test_notifications", "tooltip_notification_settings", "tooltip_mark_all_as_read", "tooltip_clear_all_notifications", "text_no_notifications", "dialog_title_clear_notifications", "dialog_content_clear_notifications", "btn_clear", "text_notification_fallback", "text_coach_details", "table_header_station", "table_header_coach", "table_header_onboard", "table_header_deboard", "table_header_vacant", "text_notification_system_tests", "text_notification_system_testing", "text_notification_test_description", "text_ready_to_run_notification_tests", "btn_run_all_tests", "btn_running_tests", "btn_individual_tests", "text_test_results", "text_no_test_results_yet", "text_run_tests_to_see_results", "text_running_comprehensive_tests", "text_all_tests_completed_successfully", "text_some_tests_failed", "text_test_execution_failed", "dialog_title_select_test", "test_fcm_token_generation", "test_firestore_token_storage", "test_complete_notification_sync", "test_cloud_function_endpoint", "test_enhanced_notification", "test_debug_firestore_token_storage", "test_force_token_refresh", "test_quick_token_check", "text_running_test", "text_test_completed_successfully", "text_test_failed", "text_no_test_data_available", "text_test_passed", "text_test_failed_label", "rail_sathi_app_bar_title", "tab_write_complaint", "tab_view_complaints", "error_could_not_launch_link", "error_failed_to_load_issues", "error_invalid_response_format", "error_failed_to_load_trip_report", "msg_data_refreshed_successfully", "error_refresh_failed", "error_select_train_date_first", "text_no_coaches_found", "text_please_select_train_date", "text_coach_label", "text_no_issues_reported", "text_reported_issues", "text_unknown_issue", "btn_manage_issues", "text_train_rake_deficiency_report", "text_rake_deficiency_report_issues", "tooltip_upload_coach_image", "btn_upload_image_video", "btn_submit_issue", "form_pnr", "form_search_by_train_number_or_name", "form_dd_mm_yyyy", "btn_next", "tab_cleaning_issues", "tab_linen_related_issues", "msg_please_fill_all_required_details", "text_ehk_not_assigned", "msg_pnr_validation_error_e", "msg_are_you_sure_delete_image", "text_selected_date", "text_date", "text_pnr", "msg_are_you_sure_delete_complaint", "reports_app_bar_title", "btn_attendance_report", "btn_round_rake_deficiency_report", "btn_train_report", "btn_detailed_attendance_report", "btn_detailed_round_trip_attendance_report", "btn_obhs_to_mcc_handover", "btn_mcc_to_obhs_handover", "btn_monthly_attendance_report", "btn_monthly_round_trip_attendance_report", "text_daily_reports", "text_monthly_reports", "btn_show_all", "btn_show_less", "section_daily_reports", "section_monthly_reports", "section_attendance_for", "report_attendance", "report_detailed_attendance", "report_detailed_round_trip", "report_obhs_to_mcc_handover", "report_monthly_attendance", "report_monthly_round_trip", "report_monthly_with_mobile", "text_notification_settings", "text_save_settings", "text_onboarding_notifications", "text_enable_onboarding_notifications", "text_station_approach_alerts", "text_boarding_alerts", "text_off_boarding_alerts", "text_proximity_alerts", "text_timing_settings", "text_advance_notice_minutes", "text_proximity_threshold_km", "text_coach_filters", "text_enable_coach_specific_filtering", "text_enabled_coach_types", "text_sound_vibration", "text_enable_sound", "text_enable_vibration", "text_advanced_settings", "text_background_notifications", "text_location_based_notifications", "text_max_notifications_per_hour", "msg_notification_preferences_saved", "msg_error_saving_preferences", "error_fetching_trains", "form_mobile", "form_enter_train_number", "form_description", "dropdown_other", "dropdown_cleaning", "dropdown_linen", "dropdown_pending", "dropdown_completed", "dialog_delete_image", "dialog_delete_image_confirm", "dialog_delete_complaint", "dialog_delete_complaint_confirm", "btn_add_image", "btn_save_changes", "btn_edit", "btn_select_date", "text_edit_complaint", "form_train_label", "error_failed_to_fetch_complaints", "text_berth_no", "msg_image_deleted", "msg_complaint_updated", "msg_update_failed", "msg_complaint_deleted_success", "msg_failed_to_delete_complaint", "snackbar_image_deleted", "snackbar_failed_to_delete_image", "snackbar_failed_to_delete_complaint", "snackbar_failed_to_fetch_complaints", "btn_rake_deficiency_report_simple", "btn_monthly_with_mobile", "btn_monthly_without_mobile", "text_info", "text_reports_for", "text_trains_not_running", "text_train_not_running", "text_please_select_relevant_trains", "text_following_trains_not_running", "text_train_label", "text_running_days_label", "text_running_trains_on", "text_train_not_running_message", "pnr_status_title", "hint_enter_pnr_number", "label_pnr_number", "btn_check_pnr", "text_no_pnr_data_found", "error_invalid_pnr_number", "error_failed_fetch_pnr", "label_train_number", "label_train_name", "label_boarding_date", "label_from", "label_to", "label_class", "label_departure", "label_arrival", "label_overall_status", "label_booking_date", "label_passenger", "label_coach_berth", "label_ehk_users", "label_ca_users", "label_obhs_users"], "te": ["text_reenter_otp", "text_deny", "text_enable", "text_location_access_required", "text_decline", "text_accept", "text_confirm_delete", "text_delete", "text_storage_permission_is", "text_please_select_a", "text_update_required", "text_update_now", "text_please_select_a_1", "text_all_details_updated", "text_failed_to_update", "text_data_refreshed_successfully", "text_train_location_saved", "text_self", "text_other_ca", "text_other_ehkobhs", "text_chart_has_not", "text_camera", "text_gallery", "text_getting_location", "text_error_message", "text_failed_to_get", "text_error_fetching_images", "text_back_to_all", "text_submit", "text_upload_status", "text_compressing_image", "text_upload_entrykeysubstring0_6", "text_image_uploading", "text_error_snapshoterror", "text_latitude_entrylatitude", "text_longitude_entrylongitude", "text_distance_entrydistance_km", "text_updated_by_entryupdatedby", "text_no_data_available", "text_show_more", "text_show_less", "text_could_not_open", "text_download_started", "text_pdf_downloaded_successfully", "text_download", "text_get_in_email", "text_could_not_launch", "text_permission_denied", "text_requested_users", "text_end_date_cannot", "text_please_select_both", "text_update_user_details", "text_request_for_update", "text_i_dont_have", "text_information", "text_request_for_sign", "text_forgot_password", "text_forgotten_password", "text_mobile_otp_login", "text_enter_otp", "text_otp_sent_to_mobile", "text_profile", "text_error", "text_sign_in_with", "text_new_user_sign", "text_please_enter_a", "text_failed_to_fetch", "text_check_pnr_status", "text_pnr_number", "text_no_pnr_data", "text_please_turn_on", "text_an_error_occurred", "text_your_current_location", "text_location_services_disabled", "text_please_enable_location", "text_location_permission_denied", "text_open_settings", "text_location_permission_denied_1", "text_refresh_failed_e", "text_no_location_data", "text_no_data_available_1", "text_no_train_details", "text_none", "text_download_pdf_for", "text_mail_pdf_for", "text_add_configuration", "text_select_charting_day", "text_submitting", "text_return_gap_updated", "text_data_not_refreshed", "text_add", "text_train_details_updated", "text_add_train_details", "text_add_train", "text_stoppages_in_sequence", "text_submitting_train_details", "text_edit_configuration", "text_no_coaches_available", "text_invalid_response_format", "text_please_select_a_2", "text_coach_handover_report", "text_save_selection", "text_select_media_type", "text_image", "text_video", "text_please_select_images", "text_please_select_at", "text_failed_to_upload", "text_error_updating_issue", "text_statustype_by", "text_no_images_available", "text_issuesubissue", "text_status", "text_submitupdate", "text_reportedby", "text_fixedby", "text_resolvedby", "text_pick_images", "text_uploading", "text_submit_upload", "text_retry", "text_approve", "text_no_requests_selected", "text_are_you_sure", "text_requeststoprocesslength_users_approved", "text_confirm_denial", "text_deny_request", "text_approve_selected", "text_approve_all", "text_processing_requests", "text_clear_search", "text_failed_to_fetch_1", "text_error_fetching_trains", "text_traintrainno_traintrainname", "text_other", "text_existing_images", "text_delete_image", "text_image_deleted", "text_newly_selected_images", "text_add_image", "text_complaint_updated", "text_update_failed", "text_save_changes", "text_delete_complaint", "text_are_you_sure_1", "text_complaint_deleted_successfully", "text_failed_to_delete", "text_select_date", "text_no_complaints_found", "text_train_no_complainttrainnumber", "text_date_complaintcomplaindate", "text_pnr_complaintpnrnumber", "text_edit", "text_complaint_submitted_successfully", "text_failed_to_submit", "text_error_e", "text_ehk_ehkdisplay", "text_pending", "text_completed", "text_upload_imagevideo", "text_submit_issue", "text_validate", "text_next", "text_error_loading_authentication", "text_storage_permission_required", "text_please_select_a_3", "text_error_json_file", "text_selection_cleared", "text_upload_json_data", "text_update", "text_add_issue", "text_no_subissues", "text_select_issue", "text_add_subissue", "text_add_new_item", "text_images_or_videos", "text_please_select_an", "text_issues_saved_upload", "text_select_issues_for", "text_pick_imagesvideos", "text_please_select_imagevideo", "text_confirm_deletion", "text_delete_report", "text_coach_issue_status", "text_both_person_and", "text_select_widgetstatustype_by", "text_subissue_widgetname", "text_issue_widgetname", "text_confirm", "text_manage_issues", "text_rake_deficiency_report", "text_upload_pnr_image", "text_pick_imagesvideos_for", "text_please_wait_until", "text_submit_feedback", "text_verify_email", "text_check_your_inbox", "text_if_not_found", "text_add_our_domain", "text_i_understand", "text_nonac", "text_select", "text_failed_to_load", "text_review_feedback", "text_deleting_feedback", "text_feedback_deleted_successfully", "text_error_deleting_feedback", "text_no_feedback_available", "text_train_no_trainnumber", "text_non_ac", "text_message", "text_job_chart_status", "text_please_select_all", "text_update_amount_for", "text_assigned", "text_amount", "text_no_image_url", "text_image_downloaded_successfully", "text_failed_to_download", "text_failed_to_download_1", "text_image_detail", "text_download_image", "text_train_trainnumber_details", "text_confirm_deactivation", "text_proceed", "text_add_email", "text_back", "text_email_verification", "text_phone_verification", "text_logout_confirmation", "text_do_you_want", "text_addupdate", "text_otp_sent_successfully", "text_failed_to_send", "text_please_enter_the", "text_failed_to_verify", "text_uploaded_at_formatteddate", "text_uploaded_by_widgetimageresponsecreatedby", "text_id_widgetimageresponseid", "text_coach_widgetimageresponsecoach", "text_issue_widgetimageresponseissue", "text_delete_confirmation", "text_are_you_sure_2", "text_save", "text_ehkca", "text_image_upload_initiated", "text_failed_to_upload_1", "text_please_select_an_1", "text_jobchart_deleted_successfully", "text_failed_to_delete_1", "text_pick_image", "text_upload_image", "text_failed_to_load_1", "text_language", "text_select_language", "text_rail_sathi", "title_location_access_required", "title_upload_status", "title_compressing_image", "title_upload_entrykeysubstring0_6", "title_permission_denied", "title_confirm_delete", "title_add_new_item", "title_add_issue", "title_add_subissue", "title_select_widgetstatustype_by", "title_update_amount_for", "form_train_number", "form_date", "form_select_date_ddmmmyyyy", "form_first_name", "form_enter_first_name", "form_middle_name_optional", "form_enter_middle_name", "form_last_name", "form_enter_last_name", "form_phone_number", "form_secondary_phone_number", "form_whatsapp_number", "form_enter_10digit_whatsapp", "form_email", "form_enter_your_email", "form_enter_your_first", "form_enter_your_middle", "form_enter_your_last", "form_enter_10digit_secondary", "form_email_1", "form_enter_your_10digit", "form_select_train_numbers", "form_zone", "form_zone_1", "form_employee_id", "form_depot", "form_reenter_password", "form_divisions", "form_divisions_1", "form_select_coaches", "form_middle_name", "form_select_train_number", "form_select_stations", "form_select_date", "form_whatsapp_number_1", "form_enter_secondary_phone", "form_mobile_number_1", "form_enter_mobile_number", "form_enter_mobile_number_1", "form_related_train", "form_division", "form_depot_1", "form_charting_day", "form_from_station", "form_to_station", "form_direction_updown", "form_start_time", "form_eg_0900_am", "form_end_time", "form_eg_0500_pm", "form_charting_time", "form_return_gap_days", "form_inout", "form_related_train_number", "form_updown", "form_train_type", "form_search_train_number", "form_coaches_comma_separated", "form_eg_h_gsl", "form_enter_coach_names", "form_select_days", "form_add_stoppage", "form_type_a_station", "form_search", "form_stoppages_in_sequence", "form_type_a_station_1", "form_frequency", "form_enter_new_coach", "form_use_comma_to", "form_add_your_comments", "form_search_by_name", "form_train", "form_complaint_type", "form_status", "form_write_your_issue", "form_issue_status", "form_name", "form_search_by_train", "form_train_selection", "form_journey_start_date", "form_ddmmyyyy", "form_coach", "form_berth", "form_issue_name", "form_subissue_name", "form_search_1", "form_widgetstatustype_by", "form_select_date_time", "form_add_your_feedback", "form_search_train_number_1", "form_train_number_1", "form_enter_otp", "form_resolved_yesno", "form_train_name_1", "form_marks_1_to", "form_remarks_by_passenger", "form_passenger_name_1", "form_pnr_number_1", "form_crn_number_1", "form_coach_no_1", "form_berth_no_1", "form_task_status_1", "form_feedback", "form_amount_in_hand", "form_select_user", "form_enter_email_otp", "form_enter_phone_otp", "form_select_coaches_optional", "btn_deny", "btn_enable", "btn_decline", "btn_accept", "btn_self", "btn_other_ca", "btn_other_ehkobhs", "btn_close", "btn_error_snapshoterror", "btn_no_data_available", "btn_your_current_location", "btn_no_location_data", "btn_no_data_available_1", "btn_add_configuration", "btn_select_charting_day", "btn_edit_configuration", "btn_no_coaches_available", "btn_coach_handover_report", "btn_statustype_by", "btn_traintrainno_traintrainname", "btn_other", "btn_no_complaints_found", "btn_pending", "btn_completed", "btn_upload_json_data", "btn_cancel", "btn_delete", "btn_save_selection", "btn_coach_issue_status", "btn_rake_deficiency_report", "btn_no_feedback_available", "btn_save", "snackbar_please_select_a", "snackbar_data_refreshed_successfully", "snackbar_train_location_saved", "snackbar_error_fetching_images", "snackbar_download_started", "snackbar_pdf_downloaded_successfully", "snackbar_could_not_launch", "snackbar_refresh_failed_e", "snackbar_return_gap_updated", "snackbar_data_not_refreshed", "snackbar_invalid_response_format", "snackbar_please_select_a_1", "snackbar_please_select_images", "snackbar_please_select_at", "snackbar_failed_to_upload", "snackbar_error_updating_issue", "snackbar_no_images_available", "snackbar_failed_to_fetch", "snackbar_error_fetching_trains", "snackbar_complaint_updated", "snackbar_update_failed", "snackbar_complaint_deleted_successfully", "snackbar_failed_to_delete", "snackbar_failed_to_submit", "snackbar_error_e", "snackbar_issues_saved_upload", "snackbar_feedback_deleted_successfully", "snackbar_error_deleting_feedback", "snackbar_job_chart_status", "snackbar_no_image_url", "snackbar_image_downloaded_successfully", "snackbar_failed_to_download", "snackbar_failed_to_download_1", "snackbar_otp_sent_successfully", "snackbar_failed_to_send", "snackbar_please_enter_the", "snackbar_email_saved_successfully", "snackbar_failed_to_verify", "snackbar_jobchart_deleted_successfully", "snackbar_failed_to_delete_1", "snackbar_failed_to_load", "text_train_details", "text_please_enable_location_services", "text_location_permissions_required", "text_location_permission_denied_forever", "text_location_permissions_permanently_denied", "text_select_all", "text_location_permissions_error", "text_refresh_failed", "text_turn_on_location_services", "text_no_location_data_available", "text_edit_train_details", "text_search_train_number", "text_select_train_number", "text_monday", "text_tuesday", "text_wednesday", "text_thursday", "text_friday", "text_saturday", "text_sunday", "text_updating_data", "text_return_gap_updated_successfully", "text_train_details_updated_successfully", "text_restrictions", "text_location", "text_time", "text_enable_media_upload", "text_failed_to_load_zones", "text_failed_to_load_divisions", "text_failed_to_load_depots", "text_failed_to_load_train_details", "text_error_loading_train_data", "text_edit_profile", "text_error_loading_authentication_state", "text_verify_email_button", "text_first_name", "text_middle_name", "text_last_name", "text_employee_number", "text_role", "text_depot", "text_phone_number", "text_whatsapp_number", "text_secondary_phone_number", "text_email", "text_phone_number_must_be_10_digits", "text_phone_number_and_secondary_phone_number_must_be_different", "text_change_email", "text_change_password", "text_change_mobile_number", "text_change_whatsapp_number", "text_update_profile", "text_add_trains", "text_up", "text_down", "text_today", "text_yesterday", "text_failed_to_load_zones_add", "text_failed_to_load_divisions_add", "text_failed_to_load_depots_add", "text_please_enter_train_number", "text_train_number_min_digits", "text_please_select_return_gap", "text_coaches_sequence", "text_please_enter_train_name", "text_please_select_charting_day", "text_please_enter_from_station", "text_please_enter_to_station", "text_please_select_direction", "text_please_enter_start_time", "text_please_enter_end_time", "text_please_enter_charting_time", "text_please_enter_train_type", "text_add_new_user", "text_phone_and_secondary_phone_must_be_different", "text_validation_error", "text_submitting_data_please_wait", "text_please_complete_all_required_fields", "text_form_incomplete", "text_enter_first_name", "text_please_enter_first_name", "text_middle_name_optional", "text_enter_middle_name", "text_enter_last_name", "text_please_enter_last_name", "text_secondary_phone_number_optional", "text_whatsapp_number_same_as_phone", "text_use_same_number_for_whatsapp", "text_enter_10_digit_whatsapp_number", "text_please_enter_whatsapp_number", "text_whatsapp_number_must_be_10_digits", "text_please_enter_only_numbers", "text_request_for_add_user", "text_information_dialog_title", "text_please_complete_fields_in_order", "text_personal_information", "text_contact_details", "text_account_settings", "text_authentication_check_failed", "text_enter_a_valid_mobile_number", "text_user_data_not_found", "text_access_token_not_found", "text_update_user", "text_enter_mobile_number", "text_search_for_a_user_to_update_their_details", "text_user_not_found_please_check_the_mobile_number_and_try_again", "text_error_with_details", "text_please_enter_your_email", "text_please_enter_valid_email", "text_invalid_email_format", "text_please_enter_valid_mobile_number", "text_please_enter_new_mobile_number", "text_do_you_want_to_logout", "text_yes", "text_no", "text_inside_train", "text_need_alarm", "text_deactivate", "text_logout", "text_email_otp_error", "text_phone_otp_error", "text_please_select_a_train_number", "text_add_update", "text_an_error_occurred_while_adding", "text_select_coaches_optional", "text_select_coaches_from_list", "text_please_select_date", "text_need_valid_email_before_deactivation", "text_send_otps_for_verification", "text_failed_to_send_otps", "text_enter_email_otp", "text_enter_phone_otp", "text_please_enter_field", "text_enable_fingerprint_login", "text_deactivate_account", "text_email_verification_optional", "text_verified", "text_email_verification_info", "text_otp_sent_message", "error_berth_number_invalid", "error_form_validation_failed", "error_max_videos", "error_max_videos_short", "error_max_feedback_images", "error_max_feedback_images_short", "error_max_images_reached", "error_picking_media", "error_email_required", "msg_email_verification_initiated", "error_otp_required", "error_something_wrong", "text_daily", "text_yes_lowercase", "text_no_lowercase", "error_berth_number_invalid_passenger", "error_no_train_selected", "error_fetching_charting_time", "dialog_train_not_running_content", "error_validating_pnr", "error_feedback_submission_passenger", "error_max_images_reached_passenger", "error_picking_media_passenger", "error_generic_passenger", "error_something_wrong_passenger", "text_check_inbox", "text_check_spam", "text_add_safe_sender", "msg_otp_sent_check_folders", "msg_error_generic", "msg_something_went_wrong", "status_yes_lowercase", "status_no_lowercase", "passenger_feedback_app_bar_title", "passenger_feedback_main_title", "form_train_name_readonly", "hint_search_train_number", "category_ac", "category_nonac", "category_tt", "category_rm", "rating_excellent", "rating_very_good", "rating_good", "rating_average", "rating_poor", "feedback_table_instruction", "feedback_table_item_header", "dialog_train_not_running", "msg_train_not_running_details", "msg_running_days_daily", "msg_running_days_na", "validation_provide_rating", "msg_rm_fields_required", "msg_upload_complete_wait", "msg_videos_limit", "msg_videos_limit_reached", "feedback_ac_item_1", "feedback_ac_item_2", "feedback_ac_item_3", "feedback_ac_item_4", "feedback_ac_item_5", "feedback_nonac_item_1", "feedback_nonac_item_2", "feedback_nonac_item_3", "button_ok", "error_fetch_train_name", "error_invalid_pnr", "success_pnr_fetched", "error_pnr_validation", "success_feedback_submitted", "error_feedback_submission", "error_unexpected", "email_verification_info_title", "email_verification_spam_notice", "email_verification_after_otp", "email_verification_understand", "success_email_verification", "error_prefix", "success_otp_verified", "upload_pnr_image", "selected_images", "no_pnr_images_selected", "upload_limit_pnr_3", "media_source_camera", "media_source_gallery", "upload_limit_pnr_max", "upload_limit_reached_message", "error_picking_media_simple", "pick_images_videos_feedback", "no_feedback_images_selected", "selected_videos", "select_media_type", "media_type_image", "media_type_video", "upload_limit_videos_3", "upload_limit_videos_max", "upload_limit_feedback_images_3", "upload_limit_feedback_images_max", "text_pnr_document", "text_feedback_media", "loading_deleting_feedback", "success_feedback_deleted", "error_deleting_feedback", "text_train_no", "text_not_verified", "btn_review", "btn_retry", "msg_no_feedback_available", "dialog_title_review_feedback", "text_status_pending_editable", "text_status_completed_not_editable", "text_pnr_documents", "text_failed_to_load_image", "text_tap_to_play_video", "form_remarks_hint", "form_resolved_yes_no", "form_marks_1_to_10", "status_none_selected", "btn_update", "validation_select_issue_type", "validation_select_sub_issue_type", "validation_select_resolved_status", "validation_select_marks", "validation_enter_valid_email", "validation_enter_otp", "validation_remarks_max_100", "msg_cannot_verify_email_completed", "msg_cannot_verify_otp_completed", "msg_otp_verified_successfully", "msg_feedback_updated_successfully", "msg_failed_to_update_feedback", "msg_failed_to_load_video", "msg_error_with_details", "msg_error_or_something_wrong", "loading_updating_feedback", "text_character_count_100", "dialog_email_verification_info_title", "dialog_email_verification_spam_notice", "dialog_after_requesting_otp", "dialog_check_inbox_first", "dialog_check_spam_folder", "dialog_add_safe_sender", "text_otp_sent_check_folders", "form_issue_type_label", "form_sub_issue_type_label", "form_task_status_label", "status_pending_option", "status_completed_option", "text_need_help_reach_out", "error_could_not_launch_phone", "text_notifications", "tooltip_test_notifications", "tooltip_notification_settings", "tooltip_mark_all_as_read", "tooltip_clear_all_notifications", "text_no_notifications", "dialog_title_clear_notifications", "dialog_content_clear_notifications", "btn_clear", "text_notification_fallback", "text_coach_details", "table_header_station", "table_header_coach", "table_header_onboard", "table_header_deboard", "table_header_vacant", "text_notification_system_tests", "text_notification_system_testing", "text_notification_test_description", "text_ready_to_run_notification_tests", "btn_run_all_tests", "btn_running_tests", "btn_individual_tests", "text_test_results", "text_no_test_results_yet", "text_run_tests_to_see_results", "text_running_comprehensive_tests", "text_all_tests_completed_successfully", "text_some_tests_failed", "text_test_execution_failed", "dialog_title_select_test", "test_fcm_token_generation", "test_firestore_token_storage", "test_complete_notification_sync", "test_cloud_function_endpoint", "test_enhanced_notification", "test_debug_firestore_token_storage", "test_force_token_refresh", "test_quick_token_check", "text_running_test", "text_test_completed_successfully", "text_test_failed", "text_no_test_data_available", "text_test_passed", "text_test_failed_label", "rail_sathi_app_bar_title", "tab_write_complaint", "tab_view_complaints", "error_could_not_launch_link", "error_failed_to_load_issues", "error_invalid_response_format", "error_failed_to_load_trip_report", "msg_data_refreshed_successfully", "error_refresh_failed", "error_select_train_date_first", "text_no_coaches_found", "text_please_select_train_date", "text_coach_label", "text_no_issues_reported", "text_reported_issues", "text_unknown_issue", "btn_manage_issues", "text_train_rake_deficiency_report", "text_rake_deficiency_report_issues", "tooltip_upload_coach_image", "btn_upload_image_video", "btn_submit_issue", "form_pnr", "form_search_by_train_number_or_name", "form_dd_mm_yyyy", "btn_next", "tab_cleaning_issues", "tab_linen_related_issues", "msg_please_fill_all_required_details", "text_ehk_not_assigned", "msg_pnr_validation_error_e", "msg_are_you_sure_delete_image", "text_selected_date", "text_date", "text_pnr", "msg_are_you_sure_delete_complaint", "reports_app_bar_title", "btn_attendance_report", "btn_round_rake_deficiency_report", "btn_train_report", "btn_detailed_attendance_report", "btn_detailed_round_trip_attendance_report", "btn_obhs_to_mcc_handover", "btn_mcc_to_obhs_handover", "btn_monthly_attendance_report", "btn_monthly_round_trip_attendance_report", "text_daily_reports", "text_monthly_reports", "btn_show_all", "btn_show_less", "section_daily_reports", "section_monthly_reports", "section_attendance_for", "report_attendance", "report_detailed_attendance", "report_detailed_round_trip", "report_obhs_to_mcc_handover", "report_monthly_attendance", "report_monthly_round_trip", "report_monthly_with_mobile", "text_notification_settings", "text_save_settings", "text_onboarding_notifications", "text_enable_onboarding_notifications", "text_station_approach_alerts", "text_boarding_alerts", "text_off_boarding_alerts", "text_proximity_alerts", "text_timing_settings", "text_advance_notice_minutes", "text_proximity_threshold_km", "text_coach_filters", "text_enable_coach_specific_filtering", "text_enabled_coach_types", "text_sound_vibration", "text_enable_sound", "text_enable_vibration", "text_advanced_settings", "text_background_notifications", "text_location_based_notifications", "text_max_notifications_per_hour", "msg_notification_preferences_saved", "msg_error_saving_preferences", "error_fetching_trains", "form_mobile", "form_enter_train_number", "form_description", "dropdown_other", "dropdown_cleaning", "dropdown_linen", "dropdown_pending", "dropdown_completed", "dialog_delete_image", "dialog_delete_image_confirm", "dialog_delete_complaint", "dialog_delete_complaint_confirm", "btn_add_image", "btn_save_changes", "btn_edit", "btn_select_date", "text_edit_complaint", "form_train_label", "error_failed_to_fetch_complaints", "text_berth_no", "msg_image_deleted", "msg_complaint_updated", "msg_update_failed", "msg_complaint_deleted_success", "msg_failed_to_delete_complaint", "snackbar_image_deleted", "snackbar_failed_to_delete_image", "snackbar_failed_to_delete_complaint", "snackbar_failed_to_fetch_complaints", "btn_rake_deficiency_report_simple", "btn_monthly_with_mobile", "btn_monthly_without_mobile", "text_info", "text_reports_for", "text_trains_not_running", "text_train_not_running", "text_please_select_relevant_trains", "text_following_trains_not_running", "text_train_label", "text_running_days_label", "text_running_trains_on", "text_train_not_running_message", "pnr_status_title", "hint_enter_pnr_number", "label_pnr_number", "btn_check_pnr", "text_no_pnr_data_found", "error_invalid_pnr_number", "error_failed_fetch_pnr", "label_train_number", "label_train_name", "label_boarding_date", "label_from", "label_to", "label_class", "label_departure", "label_arrival", "label_overall_status", "label_booking_date", "label_passenger", "label_coach_berth", "label_ehk_users", "label_ca_users", "label_obhs_users"]}